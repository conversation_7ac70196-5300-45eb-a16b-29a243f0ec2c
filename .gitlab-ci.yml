include:
  - project: "DiTP/shared-services/architektur-security/ditp-cfn-guardrules"
    file: "PAG_Security_Checks.gitlab-ci.yml"
    inputs:
      CF_TEMPLATE_PATH: "infrastructure/cdk.out"

default:
  image: node:20-alpine

stages:
  - build-docker-images
  - prepare
  - validation
  - oss-compliance
  # - perform-migration-dev
  - deploy-dev
  - after-deployment-test
  - tag-rc
  - deploy-int
  - tag-release
  - deploy-prod
  - run-migration-prod

variables:
  AWS_DEV_ACCOUNT: "************"
  AWS_INT_ACCOUNT: "************"
  AWS_PROD_ACCOUNT: "************"
  NODE_TOOLS_IMAGE: "cr.cicd.skyway.porsche.com/ditp/agile-release-trains/kas/onevms/cora/cora/nodejs20-alpine-git:latest"

.cdk-deploy:
  image: public.ecr.aws/sam/build-nodejs20.x:latest
  id_tokens:
    GITLAB_OIDC_TOKEN:
      aud: https://cicd.skyway.porsche.com
  script:
    - >
      export $(printf "AWS_ACCESS_KEY_ID=%s AWS_SECRET_ACCESS_KEY=%s AWS_SESSION_TOKEN=%s"
      $(aws sts assume-role-with-web-identity
      --role-arn "$OIDC_IAM_ROLE_ARN"
      --role-session-name "GitLabRunner-${CDK_DEFAULT_ACCOUNT}-${CI_PROJECT_ID}-${CI_PIPELINE_ID}"
      --web-identity-token ${GITLAB_OIDC_TOKEN}
      --duration-seconds 3600
      --query 'Credentials.[AccessKeyId,SecretAccessKey,SessionToken]'
      --output text))
    - cd infrastructure
    - npm config set "@kas-resources:registry=https://cicd.skyway.porsche.com/api/v4/projects/20468/packages/npm/"
    - npm config set "//cicd.skyway.porsche.com/api/v4/projects/20468/packages/npm/:_authToken=\${KAS_RESOURCES_TOKEN}"
    - npm ci --prefer-offline
    - npx aws-cdk deploy --require-approval never --all
  cache:
    - key: ${CI_COMMIT_REF_SLUG}
      paths:
        - frontend/node_modules/
        - infrastructure/node_modules/
        - infrastructure/dist/typeorm-pg-layer/
        - frontend/dist/
      policy: pull

.run-migration:
  image: public.ecr.aws/sam/build-nodejs20.x:latest
  id_tokens:
    GITLAB_OIDC_TOKEN:
      aud: https://cicd.skyway.porsche.com
  script:
    - npm config set "@kas-resources:registry=https://cicd.skyway.porsche.com/api/v4/projects/20468/packages/npm/"
    - npm config set "//cicd.skyway.porsche.com/api/v4/projects/20468/packages/npm/:_authToken=\${KAS_RESOURCES_TOKEN}"
    - cd infrastructure
    - npm ci --prefer-offline
    - >
      export $(printf "AWS_ACCESS_KEY_ID=%s AWS_SECRET_ACCESS_KEY=%s AWS_SESSION_TOKEN=%s"
      $(aws sts assume-role-with-web-identity
      --role-arn "$OIDC_IAM_ROLE_ARN"
      --role-session-name "GitLabRunner-${CDK_DEFAULT_ACCOUNT}-${CI_PROJECT_ID}-${CI_PIPELINE_ID}"
      --web-identity-token ${GITLAB_OIDC_TOKEN}
      --duration-seconds 3600
      --query 'Credentials.[AccessKeyId,SecretAccessKey,SessionToken]'
      --output text))
    - npm run migration:run:ci
  cache:
    - key: ${CI_COMMIT_REF_SLUG}
      paths:
        - infrastructure/node_modules/
      policy: pull

build-and-push-cypress-docker-image:
  stage: build-docker-images
  image: docker:latest
  services:
    - docker:dind
  variables:
    CYPRESS_IMAGE_TAG: cr.cicd.skyway.porsche.com/ditp/agile-release-trains/kas/onevms/cora/cora/cypress-nodejs20:latest
  script:
    - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" "$CI_REGISTRY"
    - cd frontend/etc
    - docker build -t "$CYPRESS_IMAGE_TAG" .
    - docker push "$CYPRESS_IMAGE_TAG"
  only:
    refs:
      - main
    changes:
      - frontend/etc/Dockerfile
  except:
    - schedules

build-and-push-nodejs-git-docker-image:
  stage: build-docker-images
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" "$CI_REGISTRY"
    - cd infrastructure/etc
    - docker build -t "$NODE_TOOLS_IMAGE" .
    - docker push "$NODE_TOOLS_IMAGE"
  only:
    refs:
      - main
    changes:
      - infrastructure/etc/Dockerfile
  except:
    - schedules

npm-install-and-build-frontend:
  stage: prepare
  image: public.ecr.aws/sam/build-nodejs20.x:latest
  id_tokens:
    GITLAB_OIDC_TOKEN:
      aud: https://cicd.skyway.porsche.com
  variables:
    OIDC_IAM_ROLE_ARN: $OIDC_IAM_ROLE_ARN_DEV
    CDK_DEFAULT_ACCOUNT: $AWS_DEV_ACCOUNT
    STAGE: "dev"
  script:
    - npm config set "@kas-resources:registry=https://cicd.skyway.porsche.com/api/v4/projects/20468/packages/npm/"
    - npm config set "//cicd.skyway.porsche.com/api/v4/projects/20468/packages/npm/:_authToken=\${KAS_RESOURCES_TOKEN}"
    - cd infrastructure
    - npm ci --prefer-offline
    - cd ..
    - cd frontend
    - npm ci --cache node_modules --prefer-offline
    - CI=false npm run build
    - cd ..
    - cd infrastructure
    - npm run build-layer
    - npm run cdk synth
  cache:
    - key: ${CI_COMMIT_REF_SLUG}
      paths:
        - frontend/node_modules/
        - infrastructure/node_modules/
        - infrastructure/dist/typeorm-pg-layer/
        - frontend/dist/
      policy: push
  artifacts:
    paths:
      - infrastructure/cdk.out/
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "main"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main"
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+.\d+.*$/'

compliance-scan:
  stage: oss-compliance
  image:
    name: amazon/aws-cli
    entrypoint: [""]
  services:
    - name: docker:dind
      alias: thedockerhost
  allow_failure: true
  only:
    - schedules
  variables:
    ORT_DOCKER_REGISTRY: 936427553375.dkr.ecr.eu-central-1.amazonaws.com
    DOCKER_TLS_CERTDIR: ""
    DOCKER_HOST: tcp://thedockerhost:2375
    DOCKER_DRIVER: overlay2
  before_script:
    - amazon-linux-extras install docker
    - aws configure set aws_access_key_id ${ORT_AWS_ACCESS_KEY_ID}
    - aws configure set aws_secret_access_key ${ORT_AWS_SECRET_ACCESS_KEY}
    - aws ecr get-login-password --region eu-central-1 | docker login --username AWS --password-stdin $ORT_DOCKER_REGISTRY
    - echo "@kas-resources:registry=https://cicd.skyway.porsche.com/api/v4/projects/20468/packages/npm/" > $PWD/frontend/.npmrc
    - echo "//cicd.skyway.porsche.com/api/v4/projects/20468/packages/npm/:_authToken=${KAS_RESOURCES_TOKEN}" >> $PWD/frontend/.npmrc
    - docker pull $ORT_DOCKER_REGISTRY/ort:latest
  script:
    - docker run -v /project/frontend/.npmrc:/home/<USER>/.npmrc -v $PWD:/project $ORT_DOCKER_REGISTRY/ort analyze -i /project/frontend -o /project/ort/
  artifacts:
    paths:
      - ort
    when: always

upload-compliance-results:
  stage: oss-compliance
  before_script:
    - apk --no-cache add zip unzip curl
  script:
    - zip -j analyzer-result.zip ort/analyzer-result.yml
    - curl -X POST https://tech-user:<EMAIL>/job/ort-porsche-scancode/buildWithParameters --form ort/analyzer-result.zip=@analyzer-result.zip --form ProjectName=cora_frontend
  only:
    - schedules
  needs:
    - compliance-scan

lint-check:
  stage: validation
  script:
    - cd infrastructure
    - npm config set "@kas-resources:registry=https://cicd.skyway.porsche.com/api/v4/projects/20468/packages/npm/"
    - npm config set "//cicd.skyway.porsche.com/api/v4/projects/20468/packages/npm/:_authToken=\${KAS_RESOURCES_TOKEN}"
    - npm ci --prefer-offline
    - npm run prettier
    - npm run lint
  cache:
    - key: ${CI_COMMIT_REF_SLUG}
      paths:
        - infrastructure/node_modules/
      policy: pull
  rules:
    - if: "$CI_COMMIT_TAG == null"

npm-audit:
  stage: validation
  script:
    - cd infrastructure
    - npm audit --omit=dev --json --audit-level high
    - cd ..
    - cd frontend
    - npm audit --omit=dev --json --audit-level high
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "main"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main"

unit-test:
  stage: validation
  script:
    - cd infrastructure
    - npm config set "@kas-resources:registry=https://cicd.skyway.porsche.com/api/v4/projects/20468/packages/npm/"
    - npm config set "//cicd.skyway.porsche.com/api/v4/projects/20468/packages/npm/:_authToken=\${KAS_RESOURCES_TOKEN}"
    - npm ci --prefer-offline
    - npm run test:unit
  cache:
    - key: ${CI_COMMIT_REF_SLUG}
      paths:
        - infrastructure/node_modules/
      policy: pull
  artifacts:
    when: always
    paths:
      - infrastructure/reports/junit.xml
      - infrastructure/coverage/**
    reports:
      junit: infrastructure/reports/junit.xml
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "main"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main"

sonarqube-check:
  stage: validation
  image:
    name: sonarsource/sonar-scanner-cli:5.0
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar" # Defines the location of the analysis task cache
    GIT_DEPTH: "0" # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - sonar-scanner
  allow_failure: true
  needs:
    - unit-test
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "main"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main"

sonarqube-vulnerability-report:
  image: node:20-alpine
  stage: validation
  script:
    - apk add --no-cache curl
    - 'curl -u "${SONAR_TOKEN}:" "https://skyway.porsche.com/sonarqube/api/issues/gitlab_sast_export?projectKey=KASHEARTBE-cora&branch=${CI_COMMIT_BRANCH}&pullRequest=${CI_MERGE_REQUEST_IID}" -o gl-sast-sonar-report.json'
  allow_failure: true
  artifacts:
    expire_in: 1 day
    reports:
      sast: gl-sast-sonar-report.json
  needs:
    - sonarqube-check
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "main"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main"

security_check:
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "main"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main"

deploy-dev:
  stage: deploy-dev
  environment: dev
  extends: .cdk-deploy
  variables:
    OIDC_IAM_ROLE_ARN: $OIDC_IAM_ROLE_ARN_DEV
    CDK_DEFAULT_ACCOUNT: $AWS_DEV_ACCOUNT
    STAGE: "dev"
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "main"
      when: always
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
      allow_failure: true
    - when: never

run-migration-dev:
  extends: .run-migration
  stage: deploy-dev
  needs:
    - deploy-dev
  environment: dev
  variables:
    OIDC_IAM_ROLE_ARN: $OIDC_IAM_ROLE_ARN_DEV
    CDK_DEFAULT_ACCOUNT: $AWS_DEV_ACCOUNT
    STAGE: "dev"
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "main"
      when: always
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
      allow_failure: true
    - when: never

integration-test:
  image: public.ecr.aws/sam/build-nodejs20.x:latest
  stage: after-deployment-test
  environment: dev
  variables:
    STAGE: "dev"
    CDK_DEFAULT_ACCOUNT: $AWS_DEV_ACCOUNT
    OIDC_IAM_ROLE_ARN: $OIDC_IAM_ROLE_ARN_DEV
  id_tokens:
    GITLAB_OIDC_TOKEN:
      aud: https://cicd.skyway.porsche.com
  before_script:
    - dnf install -y https://s3.amazonaws.com/session-manager-downloads/plugin/latest/linux_64bit/session-manager-plugin.rpm
  script:
    - npm config set "@kas-resources:registry=https://cicd.skyway.porsche.com/api/v4/projects/20468/packages/npm/"
    - npm config set "//cicd.skyway.porsche.com/api/v4/projects/20468/packages/npm/:_authToken=\${KAS_RESOURCES_TOKEN}"
    - cd infrastructure
    - npm ci --prefer-offline
    - >
      export $(printf "AWS_ACCESS_KEY_ID=%s AWS_SECRET_ACCESS_KEY=%s AWS_SESSION_TOKEN=%s"
      $(aws sts assume-role-with-web-identity
      --role-arn "$OIDC_EXECUTION_TEST_ROLE_ARN"
      --role-session-name "${CI_PROJECT_ID}-${CI_PIPELINE_ID}"
      --web-identity-token ${GITLAB_OIDC_TOKEN}
      --duration-seconds 3600
      --query 'Credentials.[AccessKeyId,SecretAccessKey,SessionToken]'
      --output text))
    - npm run test:int
  cache:
    - key: ${CI_COMMIT_REF_SLUG}
      paths:
        - infrastructure/node_modules/
      policy: pull
  artifacts:
    when: always
    paths:
      - infrastructure/reports/integration.xml
    reports:
      junit: infrastructure/reports/integration.xml
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "main"
      when: always
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
      allow_failure: true
    - when: never

e2e-tests:
  image: cr.cicd.skyway.porsche.com/ditp/agile-release-trains/kas/onevms/cora/cora/cypress-nodejs20:latest
  stage: after-deployment-test
  environment: dev
  variables:
    STAGE: "dev"
    CDK_DEFAULT_ACCOUNT: $AWS_DEV_ACCOUNT
    OIDC_IAM_ROLE_ARN: $OIDC_IAM_ROLE_ARN_DEV
  id_tokens:
    GITLAB_OIDC_TOKEN:
      aud: https://cicd.skyway.porsche.com
  artifacts:
    when: always
    paths:
      - frontend/cypress/screenshots/**
      - frontend/cypress/videos/**
    expire_in: 3 days
  before_script:
    - dnf install -y https://s3.amazonaws.com/session-manager-downloads/plugin/latest/linux_64bit/session-manager-plugin.rpm
  script:
    - >
      export $(printf "AWS_ACCESS_KEY_ID=%s AWS_SECRET_ACCESS_KEY=%s AWS_SESSION_TOKEN=%s"
      $(aws sts assume-role-with-web-identity
      --role-arn "$OIDC_EXECUTION_TEST_ROLE_ARN"
      --role-session-name "${CI_PROJECT_ID}-${CI_PIPELINE_ID}"
      --web-identity-token ${GITLAB_OIDC_TOKEN}
      --duration-seconds 3600
      --query 'Credentials.[AccessKeyId,SecretAccessKey,SessionToken]'
      --output text))
    - cd frontend
    - npx cypress install
    - npm run e2e
  cache:
    - key: ${CI_COMMIT_REF_SLUG}
      paths:
        - frontend/node_modules
      policy: pull
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "main"
      when: always
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
      allow_failure: true
    - when: never

tag-rc:
  image: $NODE_TOOLS_IMAGE
  stage: tag-rc
  when: manual
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "main"
  script:
    - FRONTEND_VERSION=$(node -p "require('./frontend/package.json').version")
    - INFRA_VERSION=$(node -p "require('./infrastructure/package.json').version")
    # Frontend and infrastructure should have the same version
    - |
      if [ "$FRONTEND_VERSION" != "$INFRA_VERSION" ]; then
        echo "Error: Versions in frontend and infrastructure package.json files do not match."
        exit 1
      fi
    - VERSION=$INFRA_VERSION
    # Strip any existing suffix (e.g., -rc.X) from the version
    - BASE_VERSION=$(echo $VERSION | cut -d'-' -f1)
    - git fetch --tags
    # Get all existing tags that match the pattern v<BASE_VERSION>-rc.<number>
    - EXISTING_TAGS=$(git tag -l "v${BASE_VERSION}-rc.*")
    - NEXT_RC=0
    - |
      if [ -n "$EXISTING_TAGS" ]; then
        HIGHEST_RC=$(echo "$EXISTING_TAGS" | sed -E 's/^.*-rc\.([0-9]+)$/\1/' | sort -n | tail -n 1)
        NEXT_RC=$((HIGHEST_RC + 1))
      fi
    - NEW_TAG="v${BASE_VERSION}-rc.${NEXT_RC}"
    - "echo Creating new tag: $NEW_TAG"
    - git tag "$NEW_TAG"
    - git push https://gitlab-ci-token:${TAG_PUSH_ACCESS_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git $NEW_TAG

deploy-int:
  stage: deploy-int
  environment: int
  extends: .cdk-deploy
  variables:
    OIDC_IAM_ROLE_ARN: $OIDC_IAM_ROLE_ARN_INT
    CDK_DEFAULT_ACCOUNT: $AWS_INT_ACCOUNT
    STAGE: "int"
  when: manual
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+.\d+-rc.*$/'

run-migration-int:
  extends: .run-migration
  stage: deploy-int
  needs:
    - deploy-int
  environment: int
  variables:
    OIDC_IAM_ROLE_ARN: $OIDC_IAM_ROLE_ARN_INT
    CDK_DEFAULT_ACCOUNT: $AWS_INT_ACCOUNT
    STAGE: "int"
  when: manual
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+.\d+-rc.*$/'

tag-release:
  image: $NODE_TOOLS_IMAGE
  stage: tag-release
  when: manual
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+.\d+-rc.*$/'
  script:
    - VERSION=$(node -p "require('./infrastructure/package.json').version")
    - BASE_VERSION=$(echo $VERSION | cut -d'-' -f1)
    - NEW_TAG="v${BASE_VERSION}"
    - "echo Creating new tag: $NEW_TAG"
    - git tag $NEW_TAG
    - git push https://gitlab-ci-token:${TAG_PUSH_ACCESS_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git $NEW_TAG

create-release:
  image: $NODE_TOOLS_IMAGE
  stage: deploy-prod
  script:
    - curl --location --output /usr/local/bin/release-cli "https://gitlab.com/api/v4/projects/gitlab-org%2Frelease-cli/packages/generic/release-cli/latest/release-cli-linux-amd64"
    - chmod +x /usr/local/bin/release-cli
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+.\d+$/'
  release:
    name: "Release $CI_COMMIT_TAG"
    description: "$CI_COMMIT_TAG_MESSAGE"
    tag_name: "$CI_COMMIT_TAG"

deploy-prod:
  stage: deploy-prod
  environment: prod
  extends: .cdk-deploy
  variables:
    OIDC_IAM_ROLE_ARN: $OIDC_IAM_ROLE_ARN_PROD
    CDK_DEFAULT_ACCOUNT: $AWS_PROD_ACCOUNT
    STAGE: "prod"
  when: manual
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+.\d+$/'

run-migration-prod:
  extends: .run-migration
  stage: run-migration-prod
  needs:
    - deploy-prod
  environment: prod
  variables:
    OIDC_IAM_ROLE_ARN: $OIDC_IAM_ROLE_ARN_PROD
    CDK_DEFAULT_ACCOUNT: $AWS_PROD_ACCOUNT
    STAGE: "prod"
  when: manual
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+.\d+$/'
