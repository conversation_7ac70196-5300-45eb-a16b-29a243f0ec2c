## Manual Migration Workflow

1. **Change the Schema**  
   Whenever you modify the database schema (entities, models, etc.), you must follow these steps:

2. **Start Migration Generation**  
   Run the migration generation script locally:

   ```bash
   npm run migration:generate:local
   ```

   This will start the SSH tunnel to Aurora and create a new migration file based on your changes.

3. **Review the Generated Migration**  
   Check the generated migration file for accuracy and completeness. Test it out if needed,

4. **Commit the Migration**  
   Add and commit the migration file in your current branch:

   ```bash
   git add ./schema-migrations/migrations/<your-migration>.ts
   git commit -m "Add schema migration for <feature>"
   git push
   ```

5. **Pipeline Applies Migration**  
   The CI/CD pipeline will automatically run the committed migration on all stages (dev, int, prod) using the Aurora Data API.

---

**Reverting:**  
To revert a migration, use:

```bash
npm run migration:revert
```

---

**Note:**

- After every schema change, you must connect to the Bastion Host and generate the migration locally.
- Migration files must always be committed to the repository so the pipeline can execute them.
- The pipeline will apply migrations for all relevant stages (int, prod).
- This process is required because integration and production only support the Data API, which cannot reliably generate migrations.
