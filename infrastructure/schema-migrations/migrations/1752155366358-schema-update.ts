import { MigrationInterface, QueryRunner } from "typeorm";

export class SchemaUpdate1752155366358 implements MigrationInterface {
    name = 'SchemaUpdate1752155366358'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "cora_purchase_intention" ALTER COLUMN "shipping_code" DROP NOT NULL`);
        if (await queryRunner.hasColumn('cora_purchase_intention', 'vehicle_status_pvms_code')) {
          await queryRunner.query(
            `ALTER TABLE cora_purchase_intention RENAME COLUMN vehicle_status_pvms_code TO vehicle_status_code;`,
          );
        }
        if (await queryRunner.hasColumn('cora_purchase_intention', 'order_status_pvms_code')) {
          await queryRunner.query(`ALTER TABLE "cora_purchase_intention" DROP COLUMN "order_status_pvms_code"`);
        }
        if (!(await queryRunner.hasColumn('cora_purchase_intention', 'vehicle_configuration_onevms'))) {
          await queryRunner.query(`ALTER TABLE "cora_purchase_intention" ADD "vehicle_configuration_onevms" jsonb`);
        }
        if (!(await queryRunner.hasColumn('cora_purchase_intention', 'is_converted'))) {
          await queryRunner.query(
            `ALTER TABLE "cora_purchase_intention" ADD "is_converted" boolean NOT NULL DEFAULT false`,
          );
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
          `ALTER TABLE cora_purchase_intention RENAME COLUMN vehicle_status_code TO vehicle_status_pvms_code;`,
        );
        await queryRunner.query(`ALTER TABLE "cora_purchase_intention" DROP COLUMN "is_converted"`);
        await queryRunner.query(`ALTER TABLE "cora_purchase_intention" DROP COLUMN "vehicle_configuration_onevms"`);
        await queryRunner.query(`ALTER TABLE "cora_purchase_intention" ADD "order_status_pvms_code" text NOT NULL`);
        await queryRunner.query(`ALTER TABLE "cora_purchase_intention" ALTER COLUMN "shipping_code" SET NOT NULL`);
    }

}
