import * as dotenv from 'dotenv';
dotenv.config();
import { DataSource } from 'typeorm';
import { getEnvVarWithAssert } from '../../lambda/utils/utils';

const IS_LOCAL = process.env.IS_LOCAL === '1';
const DB_NAME = getEnvVarWithAssert('DB_NAME');

export default new DataSource(
  IS_LOCAL
    ? {
        type: 'postgres',
        host: getEnvVarWithAssert('DB_HOST'),
        port: 5432,
        username: getEnvVarWithAssert('DB_USER'),
        password: getEnvVarWithAssert('DB_PASS'),
        database: DB_NAME,
        synchronize: false,
        dropSchema: false,
        logging: true,
        entities: ['lib/entities/*{.ts,.js}'],
        migrations: ['./schema-migrations/migrations/*.ts'],
        subscribers: [],
        migrationsTableName: 'migration_table',
        extra: {
          ssl: {
            rejectUnauthorized: false,
          },
        },
      }
    : {
        type: 'aurora-postgres',
        database: DB_NAME,
        secretArn: getEnvVarWithAssert('SECRET_ARN'),
        resourceArn: getEnvVarWithAssert('CLUSTER_ARN'),
        region: 'eu-west-1',
        synchronize: false,
        dropSchema: false,
        logging: true,
        entities: ['lib/entities/*{.ts,.js}'],
        migrations: ['./schema-migrations/migrations/*.ts'],
        subscribers: [],
        migrationsTableName: 'migration_table',
        extra: {
          ssl: {
            rejectUnauthorized: false,
          },
        },
      },
);
