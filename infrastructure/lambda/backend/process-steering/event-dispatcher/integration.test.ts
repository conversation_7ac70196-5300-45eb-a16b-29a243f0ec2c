import { DataSource, Repository } from 'typeorm';
import {
  buildLambdaArn,
  createSqsEvent,
  initDataSourceForIntTest,
  invokeGenericLambda,
} from '../../../utils/integration-test-helpers';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../lib/entities/new-car-order-model';
import {
  InboundEventDispatcherEvent,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  SpecialStatusCode,
  SQSBatchResponseWithError,
} from '../../../../lib/types/process-steering-types';
import { v4 as uuidv4 } from 'uuid';
import { Constants } from '../../../../lib/utils/constants';

const dispatcherLambdaArn = buildLambdaArn('event-dispatcher');
let dataSource: DataSource;
let ncoRepo: Repository<NewCarOrderModel>;
let savedNco: NewCarOrderModel;

const testNcoId = 'INTEGDISPATCH01';
const appsWithVisibilityImp = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'IMP',
    },
  ],
};

const testNco: NewCarOrderModel = {
  pk_new_car_order_id: testNcoId,
  dealer_number: '9690000',
  importer_code: 'DEC',
  importer_number: '9690000',
  model_type: 'MT0001',
  model_year: '2030',
  order_type: 'LF',
  quota_month: '2025-01',
  requested_dealer_delivery_date: '2025-06-01',
  shipping_code: 'SHIP01',
  receiving_port_code: 'SomePortCode',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  cnr: 'C23',
  order_status_onevms_code: 'PP2000',
  order_status_onevms_error_code: SpecialStatusCode.NONE,
  order_invoice_onevms_code: SpecialStatusCode.NONE,
  order_status_onevms_timestamp_last_change: new Date().toISOString(),
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: { ordered_options: [], created_by: 'IntegrationTester', modified_by: 'IntegrationTester' },
  configuration_expire: null,
};

const dispatcherEvent: InboundEventDispatcherEvent = {
  transaction_id: uuidv4(),
  event_type: OneVmsEventKey.CANCEL,
  action_at: new Date().toISOString(),
  source_system: OneVmsSourceSystemKey.CORA_USER,
  user_auth_context: {
    organizationId: 'SomeOrg',
    kasApplications: appsWithVisibilityImp,
    username: 'IntegrationTester',
    firstName: 'IntegrationTester',
    lastName: 'IntegrationTester',
    porschePartnerNo: 'IntegrationTester',
    displayName: 'IntegrationTester',
  },
  ncos_info: [
    {
      pk_new_car_order_id: testNco.pk_new_car_order_id,
      modified_at: testNco.modified_at!,
      sub_transaction_id: uuidv4(),
    },
  ],
  payload: { order_type: 'VF' },
};
describe('Event Dispatcher Integration Tests', () => {
  beforeAll(async () => {
    dataSource = await initDataSourceForIntTest([
      NewCarOrderModel,
      NcoConfigurationModel,
      NcoConfigOrderedOptionsModel,
    ]);
    ncoRepo = dataSource.getRepository(NewCarOrderModel);

    savedNco = await ncoRepo.save(testNco);
    testNco.modified_at = savedNco.modified_at;
    const ncoEntry = dispatcherEvent.ncos_info![0];
    ncoEntry.modified_at = savedNco.modified_at!;
  });

  afterAll(async () => {
    await ncoRepo.delete({ pk_new_car_order_id: testNcoId });
    await dataSource.destroy();
  });

  beforeEach(async () => {
    savedNco = await ncoRepo.save(testNco);
    testNco.modified_at = savedNco.modified_at;
    const ncoEntry = dispatcherEvent.ncos_info![0];
    ncoEntry.modified_at = savedNco.modified_at!;
  });

  it('Dispatch should fail for invalid SQS message.', async () => {
    const sqsEvent = createSqsEvent([{ ...dispatcherEvent, event_type: undefined }]);
    const result = await invokeGenericLambda<SQSBatchResponseWithError>(dispatcherLambdaArn, sqsEvent);

    expect(result.batchItemFailures).toHaveLength(1);
    expect(result.batchItemFailures[0].errorMessage).toContain('Failed to parse/validate SQS record');
  });

  it('Dispatch should fail if NCO is not found in database', async () => {
    const nonExistentId = 'UNKNOWN_NCO_ID';
    const sqsEvent = createSqsEvent([
      {
        ...dispatcherEvent,
        ncos_info: [{ ...dispatcherEvent.ncos_info![0], pk_new_car_order_id: nonExistentId }],
      },
    ]);
    const result = await invokeGenericLambda<SQSBatchResponseWithError>(dispatcherLambdaArn, sqsEvent);
    expect(result.batchItemFailures).toHaveLength(1);
    expect(result.batchItemFailures[0].errorMessage).toContain('Referenced NCO not found in database');
  });

  it('Dispatch should fail on modified_at mismatch (racing condition)', async () => {
    const sqsEvent = createSqsEvent([
      {
        ...dispatcherEvent,
        ncos_info: [
          { ...dispatcherEvent.ncos_info![0], pk_new_car_order_id: testNcoId, modified_at: new Date().toISOString() },
        ],
      },
    ]);
    const result = await invokeGenericLambda<SQSBatchResponseWithError>(dispatcherLambdaArn, sqsEvent);
    expect(result.batchItemFailures).toHaveLength(1);
    expect(result.batchItemFailures[0].errorMessage).toContain('Racing condition occurred');
  });

  it('Dispatch should handle partial failures when multiple NCOs are included', async () => {
    const unknownNcoId = 'UNKNOWN123';
    const sqsEvent = createSqsEvent([
      {
        ...dispatcherEvent,
        ncos_info: [
          { pk_new_car_order_id: testNcoId, modified_at: savedNco.modified_at, sub_transaction_id: uuidv4() },
          { pk_new_car_order_id: unknownNcoId, modified_at: savedNco.modified_at, sub_transaction_id: uuidv4() },
        ],
      },
    ]);
    const result = await invokeGenericLambda<SQSBatchResponseWithError>(dispatcherLambdaArn, sqsEvent);
    expect(result.batchItemFailures).toHaveLength(1);
    expect(result.batchItemFailures[0].errorMessage).toContain('Referenced NCO not found in database');
  });

  it('Dispatch should run successfully', async () => {
    const sqsEvent = createSqsEvent([dispatcherEvent]);
    const result = await invokeGenericLambda<SQSBatchResponseWithError>(dispatcherLambdaArn, sqsEvent);

    expect(result.batchItemFailures).toBeDefined();
    expect(result.batchItemFailures.length).toBe(0);
  });
});
