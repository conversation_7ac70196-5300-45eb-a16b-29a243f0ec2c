import { OneVmsEventKey, OneVmsSourceSystemKey } from '../../../../../lib/types/process-steering-types';
import { auroraUnitTestSecret, dummyKafkaSecret, mockContext, setupMocks } from '../../../../utils/test-utils';
import { P06NewCarOrderDataDTO } from '../../../../../lib/types/p06-types';
import oldNewCarOrderJson from '../../../../../test/data/old_new_car_order.json';
import * as utilsTypeOrm from '../../../../utils/utils-typeorm';

const mocks = setupMocks({
  mockGetExistingNco: { returnNco: oldNewCarOrderJson as NewCarOrderModel },
});
jest.mock('../../../../utils/kafka');

import { handler } from './index';
import { GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';
import { MSKEvent, MSKRecord } from 'aws-lambda';
import { EventImportContext } from '../event-import-context';
import { NewCarOrderModel } from '../../../../../lib/entities/new-car-order-model';
import { createKafkaRecord, createKafkaEvent } from '../../../../utils/integration-test-helpers';
EventImportContext.sendToDispatcherSqs = jest.fn().mockResolvedValue(undefined);

beforeEach(() => {
  mocks.smMock!.reset();
  jest.resetModules();
  (EventImportContext.sendToDispatcherSqs as jest.Mock).mockClear();
});
describe('P06 Import handler', () => {
  beforeEach(() => {
    mocks
      .smMock!.on(GetSecretValueCommand, { SecretId: process.env.KAFKA_SECRET_ARN })
      .resolves({ SecretString: JSON.stringify(dummyKafkaSecret) });
    mocks
      .smMock!.on(GetSecretValueCommand, { SecretId: process.env.AURORA_SECRET_ARN })
      .resolves({ SecretString: JSON.stringify(auroraUnitTestSecret) });
  });

  const buildMSKEvent = (payload: Partial<P06NewCarOrderDataDTO>): MSKEvent => {
    const record: MSKRecord = createKafkaRecord(OneVmsSourceSystemKey.P06, 'P06_topic', payload);
    return createKafkaEvent([record]);
  };

  const completeTestValue: P06NewCarOrderDataDTO = {
    ids: { new_car_order_id: 'DCSUECQN' },
    model_info: {
      model_year: 2023,
      model_year_code: '23',
      transmission_type: 'auto',
    },
    order_info: {
      base_info: {
        quota_month: '2023-10',
        order_type: 'OT',
        is_locator_visible: true,
      },
      trading_partner: {
        importer_code: 'IMP001',
        importer_number: '9690000',
        dealer_ship_to_number: 'D1001',
      },
      status_info: {
        vehicle_status_piaom_code: 'VS001',
        vehicle_status_piaom_timestamp: '2023-10-01T00:00:00Z',
        order_status_piaom_code: 'OS001',
        order_status_piaom_timestamp: '2023-10-01T00:00:00Z',
      },
      sales_info: {
        cancellation_reason: 'WhyCancelSuchANiceCar',
      },
      planning: {
        has_checked_quota: true,
      },
    },
    logistics_info: {
      shipping_code: 'SC001',
      shipping_block: 'Block-A',
    },
    production_info: {
      production_plant: 'Plant-1',
    },
    appointment_date_info: {
      production_logistic_dates: {
        order_creation_date: '2023-10-01T00:00:00Z',
      },
      process_specific: {
        factory_pickup_status: 'Ready',
        factory_pickup_date: '2023-10-22T00:00:00Z',
      },
    },
    vehicle_technical_info: {
      general: {
        combustion_engine_number: 'ENG123',
        tire_code: 'TIRE123',
        key_code: 'KEY123',
        emission_standard: 'Euro 6',
      },
    },
    resolved_config_options: [{ option_code: 'OPT1', option_id: 'ID212' }],
  };

  const sendToDispatcher = jest.spyOn(EventImportContext, 'sendToDispatcherSqs');

  it('should dispatch the event if the NewCarOrder exists', async () => {
    const event = buildMSKEvent(completeTestValue);

    await handler(event, mockContext, () => {});
    expect(sendToDispatcher).toHaveBeenCalled();

    const dispatchedEvent = sendToDispatcher.mock.calls[0][0];
    expect(dispatchedEvent.event_type).toEqual(OneVmsEventKey.P06_UPDATE_NCO);
    expect(dispatchedEvent.source_system).toEqual(OneVmsSourceSystemKey.P06);
    expect(dispatchedEvent.ncos_info?.[0].pk_new_car_order_id).toEqual('DCSUECQN');
  });

  it('should not dispatch event if schema is not valid', async () => {
    const event = buildMSKEvent({
      ...completeTestValue,
      order_info: undefined,
    });

    await handler(event, mockContext, () => {});

    expect(sendToDispatcher).not.toHaveBeenCalled();
  });

  it('should not dispatch event if Importer is not relevant', async () => {
    const event: MSKEvent = buildMSKEvent({
      ...completeTestValue,
      order_info: {
        ...completeTestValue.order_info,
        trading_partner: {
          ...completeTestValue.order_info.trading_partner,
          importer_number: 'RandomImporter',
        },
      },
    });

    await handler(event, mockContext, () => {});

    expect(sendToDispatcher).not.toHaveBeenCalled();
  });

  it('should not dispatch if NewCarOrder does not exist', async () => {
    jest.spyOn(utilsTypeOrm, 'getExistingNco').mockResolvedValueOnce(Promise.resolve(null));

    const event = buildMSKEvent({
      ...completeTestValue,
      ids: {
        new_car_order_id: 'NON_EXISTING_ID',
      },
    });

    await handler(event, mockContext, () => {});

    expect(sendToDispatcher).not.toHaveBeenCalled();
  });
});
