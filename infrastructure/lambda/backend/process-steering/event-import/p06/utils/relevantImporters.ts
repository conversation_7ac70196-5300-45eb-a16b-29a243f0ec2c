import { KasStage } from '@kas-resources/constructs';

export const relevantImporters: Record<KasStage, string[]> = {
  dev: [
    '9690000', //Deutschland
    '4500000', //USA
    '7070000', //Brunei
    '7220000', //Mongolei
    '8030000', //Neukaledonien
    '8250000', //Neu Seeland
    '7860000', //Singapore
    '6610000', //Thailand
    '7030000', //Vietnam
    '8040000', //Kolumbien
    '7590000', //Mexico
    '8270000', //Puerto Rico
    '6210000', //Guatemala
  ],
  int: [
    '9690000', //Deutschland
    '4500000', //USA
    '7070000', //Brunei
    '7220000', //Mongolei
    '8030000', //Neukaledonien
    '8250000', //Neu Seeland
    '7860000', //Singapore
    '6610000', //Thailand
    '7030000', //Vietnam
    '8040000', //Kolumbien
    '7590000', //Mexico
    '8270000', //Puerto Rico
    '6210000', //Guatemala
  ],
  prod: [
    '9690000', //Deutschland
  ],
};
