import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';

import { getVisibilityLevel, sanitizeApiGwEvent, sendFail, sendSuccess } from '../../utils/api-helpers';

import { getAllowedDealers } from '../../utils/validation-helpers';
import { getEnvVarWithAssert } from '../../utils/utils';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { createApiGwHandler } from '../../utils/api-gw-handler';
import { createTypeORMDataSource } from '../../config/typeorm-config';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../lib/entities/new-car-order-model';
import { secretCache } from '../../utils/secret-cache';
import { ObjectValidator } from '../../../lib/utils/object-validation';
import { CoraNCOGetApiResponseNoConfig, CoraNCOQueryApiRequestBody } from '../../../lib/types/new-car-order-types';
import {
  agGridSsrmCreateWhereSql,
  applyInboundMappingFilterNegative,
  applyInboundMappingFilterPositive,
  hasEmptySetFilter,
} from './query-utils';
import { ModelTypeVisibilityModel } from '../../../lib/entities/model-type-visibility-model';
import { InboundProcessMappingModel } from '../../../lib/entities/inbound-mapping-model';

const dynamoDb = new DynamoDBClient({ region: process.env.AWS_REGION });
const applicationNameToAuthorize = getEnvVarWithAssert('APPLICATION_NAME_TO_AUTHORIZE');
//initialize secret chache with required secrets
const AURORA_SECRET_ARN = getEnvVarWithAssert('AURORA_SECRET_ARN');
let columnsToSelect: string[] = [];
secretCache.initCache(AURORA_SECRET_ARN);

const objectValidator = new ObjectValidator<CoraNCOQueryApiRequestBody>('CoraNCOQueryApiRequestBody');

const fetchOrdersFunc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  logger.log(LogLevel.DEBUG, 'Fetching new car orders for dealer', { data: sanitizeApiGwEvent({ event }, logger) });

  if (!event.body) {
    return sendFail(
      {
        message: 'Invalid Queryparameters. No request body',
        status: 400,
        reqHeaders: event.headers,
      },
      logger,
    );
  }
  let queryParameters: unknown = {};
  try {
    queryParameters = JSON.parse(event.body) ?? {};
  } catch (error) {
    logger.log(LogLevel.WARN, 'invalid event body', { data: error });
    return sendFail(
      {
        message: 'Invalid Event Body',
        status: 400,
        reqHeaders: event.headers,
      },
      logger,
    );
  }
  const [validatedQueryParams, validation_errors] = objectValidator.validate(queryParameters);
  if (validatedQueryParams === null) {
    logger.log(LogLevel.WARN, 'ajv validation failed', { data: validation_errors });
    return sendFail(
      {
        message: 'Invalid Queryparameters. Validation failed with: ' + JSON.stringify(validation_errors),
        status: 400,
        reqHeaders: event.headers,
      },
      logger,
    );
  }
  logger.log(LogLevel.DEBUG, 'Validated filters', { data: validatedQueryParams });

  const offset = validatedQueryParams.startRow ?? 0;
  const limit = validatedQueryParams.endRow ? validatedQueryParams.endRow - offset : 100;

  // Get Allowed Dealers
  const allowedDealers = await getAllowedDealers({ dynamoDb, event }, logger);
  if (allowedDealers.length === 0) {
    return sendFail(
      { message: 'User is not authorized to any dealers', status: 403, reqHeaders: event.headers },
      logger,
    );
  }

  const visibilityLevel = getVisibilityLevel({ event, applicationNameToAuthorize }, logger);
  if (!visibilityLevel) {
    logger.log(LogLevel.WARN, 'Failed to get the visibility level', {
      data: sanitizeApiGwEvent({ event: event }, logger),
    });
    return sendFail({ message: 'Auth context is missing', status: 401, reqHeaders: event.headers }, logger);
  }

  let newCarOrders: NewCarOrderModel[] = [];

  //perform rds query to get all relevant orders
  try {
    const dataSource = await createTypeORMDataSource(logger, AURORA_SECRET_ARN, 'prod', [
      //stage hardcoded because w/e?? schema sync can not work with a reader secret
      NewCarOrderModel,
      NcoConfigurationModel,
      NcoConfigOrderedOptionsModel,
      ModelTypeVisibilityModel,
      InboundProcessMappingModel,
    ]);
    // Build the query
    const ncoRepository = dataSource.getRepository(NewCarOrderModel);
    const queryBuilder = ncoRepository.createQueryBuilder('new_car_order');
    logger.log(LogLevel.DEBUG, `PARAM: `, { data: validatedQueryParams.eventFilters });

    if (validatedQueryParams.eventFilters && validatedQueryParams.eventFilters.length > 0) {
      const inboundMappingRepo = dataSource.getRepository(InboundProcessMappingModel);

      for (const filterItem of validatedQueryParams.eventFilters) {
        const mappings = await inboundMappingRepo.find({
          where: {
            event: filterItem.event,
          },
        });
        logger.log(LogLevel.DEBUG, `MAPPING for event ${filterItem.event}`, { data: mappings });

        if (filterItem.negated === true) {
          applyInboundMappingFilterNegative(queryBuilder, mappings, logger);
        } else {
          applyInboundMappingFilterPositive(queryBuilder, mappings, logger);
        }
      }
    }

    // Get Columns to select (all columns and then remove unwanted, could be hardcoded)
    if (columnsToSelect.length === 0) {
      columnsToSelect = ncoRepository.metadata.columns
        .map((col) => `new_car_order.${col.propertyName}`)
        .filter((col) => col !== 'new_car_order.configuration_expire');
      logger.log(LogLevel.DEBUG, 'columns to get', { data: columnsToSelect });
    }

    // Optimise to only get non-configuration columns
    queryBuilder.select(columnsToSelect);

    // Limit Results to allowed dealers
    const allDealerNrs = allowedDealers.map((rel) => rel.dealer_number).filter((dealNr) => dealNr !== undefined);
    logger.log(LogLevel.DEBUG, `AllowedDealers: ${allDealerNrs.join(',')}`);
    queryBuilder.andWhere('new_car_order.dealer_number IN (:...dealerNumbers)', { dealerNumbers: allDealerNrs });

    if (hasEmptySetFilter(validatedQueryParams.filterModel)) {
      logger.log(LogLevel.DEBUG, `Filter contains empty set filter. Returning empty result.`);
      return sendSuccess({ body: { data: [], rowCount: 0 }, reqHeaders: event.headers }, logger);
    }

    if (validatedQueryParams.filterModel) {
      agGridSsrmCreateWhereSql(queryBuilder, validatedQueryParams.filterModel, logger);
    }

    const mtv_from = new Date().toISOString().split('T', 1)[0];
    queryBuilder.innerJoin(
      ModelTypeVisibilityModel,
      'mtv',
      'new_car_order.cnr = mtv.cnr AND new_car_order.model_year = mtv.my4 AND new_car_order.importer_number = mtv.importer_number AND new_car_order.model_type = mtv.model_type',
    );
    queryBuilder.andWhere('mtv.role = :role', { role: visibilityLevel });
    queryBuilder.andWhere('mtv.valid_from <= :valid_min', { valid_min: mtv_from });

    queryBuilder.limit(limit);
    queryBuilder.offset(offset);
    if (validatedQueryParams.sortModel && validatedQueryParams.sortModel.length > 0) {
      // need the alias here because of the mtv innerjoin
      queryBuilder.orderBy(
        `new_car_order.${validatedQueryParams.sortModel[0].colId}`,
        validatedQueryParams.sortModel[0].sort === 'desc' ? 'DESC' : 'ASC',
      );
    }
    logger.log(LogLevel.DEBUG, 'Query', {
      data: { query: queryBuilder.getQuery(), params: queryBuilder.getParameters() },
    });
    // Variable cannot be declared here because needs to exist outside try/catch
    newCarOrders = await queryBuilder.getMany();

    logger.log(LogLevel.DEBUG, `Got db response for newCarOrders`, { data: newCarOrders.length });
  } catch (error) {
    logger.log(LogLevel.ERROR, `Failed to get newCarOrders from RDS`, { data: error });
    return sendFail(
      {
        message: `Internal Server Error, CorrelationId: ${logger.getCorrelationId()}`,
        status: 500,
        reqHeaders: event.headers,
      },
      logger,
    );
  }

  if (newCarOrders.length === 0) {
    logger.log(LogLevel.DEBUG, `Empty DB Response or unknown error`);
    return sendSuccess({ body: { data: [], rowCount: 0 }, reqHeaders: event.headers }, logger);
  }

  logger.log(LogLevel.DEBUG, 'NewCarOrders', { data: newCarOrders });

  //add dealer name to result
  const newCarOrdersFilteredWithDealerName: CoraNCOGetApiResponseNoConfig[] = newCarOrders.map((nco) => ({
    ...nco,
    business_partner_id: nco.business_partner_id ?? '',
    created_at: nco.created_at ?? '',
    created_by: nco.created_by,
    modified_at: nco.modified_at ?? nco.created_at!,
    requested_dealer_delivery_date: nco.requested_dealer_delivery_date ?? '',
    dealer_name: allowedDealers.find((dlr) => dlr.dealer_number === nco.dealer_number)?.display_name,
  }));
  return sendSuccess(
    { body: { data: newCarOrdersFilteredWithDealerName, rowCount: -1 }, reqHeaders: event.headers },
    logger,
  );
};

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGwHandler('fetch-new-car-orders-for-dealer', LogLevel.TRACE)(event, context, fetchOrdersFunc);
