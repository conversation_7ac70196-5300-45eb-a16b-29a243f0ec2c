import { DataSource, In, Repository } from 'typeorm';
import {
  buildLambdaArn,
  cleanupDynamodb,
  createApiGwEvent,
  initDataSourceForIntTest,
  invokeApiGwLambda,
  prepareDynamodb,
} from '../../utils/integration-test-helpers';
import { Constants, OneVmsEventHandlerKey } from '../../../lib/utils/constants';
import { OneVmsEventKey, OneVmsSourceSystemKey, SpecialStatusCode } from '../../../lib/types/process-steering-types';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../lib/entities/new-car-order-model';
import { v4 as uuidv4 } from 'uuid';
import { OneVmsStatusModel } from '../../../lib/entities/onevms-status-model';
import { CoraMdOneVmsStatusType } from '../../../lib/types/masterdata-types';
import { InboundProcessMappingModel } from '../../../lib/entities/inbound-mapping-model';
import {
  CoraNCOBaseApiRequest,
  CoraNCOQueryApiRequestBody,
  CoraNCOQueryApiResponse,
} from '../../../lib/types/new-car-order-types';
import { ncoApiToDbObj } from '../../utils/utils-typeorm';
import { CoraOrgRelModel } from '../../../lib/types/boss-org-types';
import { ModelTypeVisibilityModel } from '../../../lib/entities/model-type-visibility-model';
const lambdaArn = buildLambdaArn('get-order-list');
let dataSource: DataSource;
let ncoRepo: Repository<NewCarOrderModel>;
let mappingRepo: Repository<InboundProcessMappingModel>;
let statusRepo: Repository<OneVmsStatusModel>;
let mtvRepo: Repository<ModelTypeVisibilityModel>;
// Test order IDs
const matchingOrderId = 'TEST_MATCHING_ORDER';
const nonMatchingOrderId1 = 'TEST_NON_MATCHING_1';
const nonMatchingOrderId2 = 'TEST_NON_MATCHING_2';

// Test statuses
const matchingOrderStatus: OneVmsStatusModel = {
  status_code: 'MATCH_STATUS',
  status_type: CoraMdOneVmsStatusType.Order,
  status_description_DE: 'Matching Test Status',
  status_description_EN: 'Matching Test Status',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
};

const nonMatchingOrderStatus: OneVmsStatusModel = {
  status_code: 'NON_MATCH_STATUS',
  status_type: CoraMdOneVmsStatusType.Order,
  status_description_DE: 'Non-Matching Test Status',
  status_description_EN: 'Non-Matching Test Status',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
};

const matchingInvoiceStatus: OneVmsStatusModel = {
  status_code: 'MATCH_INV',
  status_type: CoraMdOneVmsStatusType.Invoice,
  status_description_DE: 'Matching Invoice',
  status_description_EN: 'Matching Invoice',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
};

const nonMatchingInvoiceStatus: OneVmsStatusModel = {
  status_code: 'NON_MATCH_INV',
  status_type: CoraMdOneVmsStatusType.Invoice,
  status_description_DE: 'Non-Matching Invoice',
  status_description_EN: 'Non-Matching Invoice',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
};

// Test mapping - only one mapping for both positive and negative filtering
const testMapping: InboundProcessMappingModel = {
  id: uuidv4(),
  order_status_code: matchingOrderStatus.status_code,
  error_status_code: SpecialStatusCode.NONE,
  invoice_status_code: matchingInvoiceStatus.status_code,
  event: OneVmsEventKey.INTEGRATION_TEST,
  source_system: OneVmsSourceSystemKey.CORA_SYSTEM,
  target_event_handler: OneVmsEventHandlerKey.INTEGRATION_TEST,
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
};

const orgRelTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName);
//org relations test data
const importerOrgId = 'importerOrgId-32bf56d50169';
const dealerGroupOrgId = 'dealerGroupOrgId-2c6c7ed32b68';
const dealerOrgId1 = 'dealerOrgId1-996c28a98f90';
const dealerOrgId2 = 'dealerOrgId2-7bac1c7500b3';
const unrelatedDealerOrgId = 'unrelatedDealerOrgId-************';
const orgWithoutDealers = 'orgWithoutDealers-c41f760e2d0e';

const orgRels: CoraOrgRelModel[] = [
  //importer relation to itself
  {
    pk_ppn_id: importerOrgId,
    parent_ppn_id: importerOrgId,
    dealer_number: 'ItNcoGetImp',
    display_name: 'IntegrationTest - Importer',
    importer_number: 'ItNcoGetImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //org without dealers relation to itself
  {
    pk_ppn_id: orgWithoutDealers,
    parent_ppn_id: orgWithoutDealers,
    display_name: 'IntegrationTest - Importer2',
    importer_number: 'ItNcoGetImp2',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group relation to parent importer
  {
    pk_ppn_id: dealerGroupOrgId,
    parent_ppn_id: importerOrgId,
    display_name: 'IntegrationTest - Dealer Group',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer group relation to itself
  {
    pk_ppn_id: dealerGroupOrgId,
    parent_ppn_id: dealerGroupOrgId,
    display_name: 'IntegrationTest - Dealer Group',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer1 relation to parent dealer group
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: dealerGroupOrgId,
    dealer_number: 'ItNcoGetDlr1',
    display_name: 'IntegrationTest - Dealer1',
    importer_number: 'ItNcoGetImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer1 relation to parent importer
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: importerOrgId,
    dealer_number: 'ItNcoGetDlr1',
    display_name: 'IntegrationTest - Dealer1',
    importer_number: 'ItNcoGetImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer1 relation to itself
  {
    pk_ppn_id: dealerOrgId1,
    parent_ppn_id: dealerOrgId1,
    dealer_number: 'ItNcoGetDlr1',
    display_name: 'IntegrationTest - Dealer1',
    importer_number: 'ItNcoGetImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer2 relation to parent dealer group
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: dealerGroupOrgId,
    dealer_number: 'ItNcoGetDlr2',
    display_name: 'IntegrationTest - Dealer2',
    importer_number: 'ItNcoGetImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer2 relation to parent importer
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: importerOrgId,
    dealer_number: 'ItNcoGetDlr2',
    display_name: 'IntegrationTest - Dealer2',
    importer_number: 'ItNcoGetImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //dealer2 relation to itself
  {
    pk_ppn_id: dealerOrgId2,
    parent_ppn_id: dealerOrgId2,
    dealer_number: 'ItNcoGetDlr2',
    display_name: 'IntegrationTest - Dealer2',
    importer_number: 'ItNcoGetImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
  //unrelated dealer relation to itself
  {
    pk_ppn_id: unrelatedDealerOrgId,
    parent_ppn_id: unrelatedDealerOrgId,
    dealer_number: 'ItUnrelatedGetDlr',
    display_name: 'IntegrationTest - Unrelated Dlr',
    importer_number: 'ItUnrelatedGetImp',
    is_deactivated: false,
    ppn_status: 'OPERATIVE',
    is_relevant_for_order_create: true,
  },
];
const orgRelPks = orgRels.map((org) => ({
  pk_ppn_id: org.pk_ppn_id,
  parent_ppn_id: org.parent_ppn_id,
}));
function transformBeforeSave(nco: NewCarOrderModel): NewCarOrderModel {
  return ncoApiToDbObj(nco as unknown as CoraNCOBaseApiRequest, 'IntegrationTester', nco);
}
// Test orders
const matchingOrder: NewCarOrderModel = {
  dealer_number: 'ItNcoGetDlr1',
  importer_code: 'IT',
  importer_number: 'ItNcoGetImp',
  model_type: 'MT0001',
  model_year: '2030',
  order_type: 'LF',
  quota_month: '2025-01',
  requested_dealer_delivery_date: '2025-06-01',
  shipping_code: 'SHIP01',
  receiving_port_code: 'SomePortCode',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  cnr: 'C_INT_TEST',
  order_status_onevms_timestamp_last_change: new Date().toISOString(),
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: { ordered_options: [], created_by: 'IntegrationTester', modified_by: 'IntegrationTester' },
  configuration_expire: null,
  pk_new_car_order_id: matchingOrderId,
  order_status_onevms_code: matchingOrderStatus.status_code,
  order_status_onevms_error_code: SpecialStatusCode.NONE,
  order_invoice_onevms_code: matchingInvoiceStatus.status_code,
};

const nonMatchingOrder1: NewCarOrderModel = {
  dealer_number: 'ItNcoGetDlr2',
  importer_code: 'IT',
  importer_number: 'ItNcoGetImp',
  model_type: 'MT0001',
  model_year: '2030',
  order_type: 'LF',
  quota_month: '2025-01',
  requested_dealer_delivery_date: '2025-06-01',
  shipping_code: 'SHIP01',
  receiving_port_code: 'SomePortCode',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  cnr: 'C_INT_TEST',
  order_status_onevms_timestamp_last_change: new Date().toISOString(),
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: { ordered_options: [], created_by: 'IntegrationTester', modified_by: 'IntegrationTester' },
  configuration_expire: null,
  pk_new_car_order_id: nonMatchingOrderId1,
  order_status_onevms_code: nonMatchingOrderStatus.status_code, // Different order status
  order_status_onevms_error_code: SpecialStatusCode.NONE,
  order_invoice_onevms_code: matchingInvoiceStatus.status_code,
};

const nonMatchingOrder2: NewCarOrderModel = {
  dealer_number: 'ItNcoGetDlr2',
  importer_code: 'IT',
  importer_number: 'ItNcoGetImp',
  model_type: 'MT0001',
  model_year: '2030',
  order_type: 'LF',
  quota_month: '2025-01',
  requested_dealer_delivery_date: '2025-06-01',
  shipping_code: 'SHIP01',
  receiving_port_code: 'SomePortCode',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  cnr: 'C_INT_TEST',
  order_status_onevms_timestamp_last_change: new Date().toISOString(),
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: { ordered_options: [], created_by: 'IntegrationTester', modified_by: 'IntegrationTester' },
  configuration_expire: null,
  pk_new_car_order_id: nonMatchingOrderId2,
  order_status_onevms_code: matchingOrderStatus.status_code,
  order_status_onevms_error_code: SpecialStatusCode.NONE,
  order_invoice_onevms_code: nonMatchingInvoiceStatus.status_code, // Different invoice status
};
const mtvTest: ModelTypeVisibilityModel = {
  importer_number: 'ItNcoGetImp',
  cnr: 'C_INT_TEST',
  model_type: 'MT0001',
  my4: '2030',
  role: 'IMP',
  valid_from: new Date().toISOString().split('T')[0],
  created_by: 'IntegrationTester',
  created_at: new Date().toISOString(),
  modified_by: 'IntegrationTester',
  modified_at: new Date().toISOString(),
};

// Applications with visibility
const appsWithVisibility = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'IMP',
    },
  ],
};
describe('Fetch Orders Integration Tests', () => {
  beforeAll(async () => {
    dataSource = await initDataSourceForIntTest([
      NewCarOrderModel,
      NcoConfigurationModel,
      NcoConfigOrderedOptionsModel,
      InboundProcessMappingModel,
      OneVmsStatusModel,
      ModelTypeVisibilityModel,
    ]);
    ncoRepo = dataSource.getRepository(NewCarOrderModel);
    mappingRepo = dataSource.getRepository(InboundProcessMappingModel);
    statusRepo = dataSource.getRepository(OneVmsStatusModel);
    mtvRepo = dataSource.getRepository(ModelTypeVisibilityModel);
    await mtvRepo.save(mtvTest);
    // Save statuses
    await statusRepo.save(matchingOrderStatus);
    await statusRepo.save(nonMatchingOrderStatus);
    await statusRepo.save(matchingInvoiceStatus);
    await statusRepo.save(nonMatchingInvoiceStatus);

    // Save mapping (only one)
    await mappingRepo.save(testMapping);

    // Save test orders
    await ncoRepo.save(transformBeforeSave(matchingOrder));
    await ncoRepo.save(transformBeforeSave(nonMatchingOrder1));
    await ncoRepo.save(transformBeforeSave(nonMatchingOrder2));
    await prepareDynamodb([{ tableName: orgRelTableName, objs: orgRels }]);
  }, 30000);

  afterAll(async () => {
    // Clean up test orders
    await ncoRepo.delete({
      pk_new_car_order_id: In([matchingOrderId, nonMatchingOrderId1, nonMatchingOrderId2]),
    });

    // Clean up mapping
    await mappingRepo.delete({
      id: testMapping.id,
    });

    // Clean up statuses
    await statusRepo.delete({
      status_code: In([
        matchingOrderStatus.status_code,
        nonMatchingOrderStatus.status_code,
        matchingInvoiceStatus.status_code,
        nonMatchingInvoiceStatus.status_code,
      ]),
    });
    await mtvRepo.delete({
      importer_number: mtvTest.importer_number,
      cnr: mtvTest.cnr,
      model_type: mtvTest.model_type,
      my4: mtvTest.my4,
      role: mtvTest.role,
    });

    await dataSource.destroy();
    await cleanupDynamodb([
      {
        tableName: orgRelTableName,
        pks: orgRelPks,
      },
    ]);
  }, 30000);

  it('should return only orders matching the mapping with positive filter (negated=false)', async () => {
    // Create request body with positive event filter (negated=false)
    const requestBody: CoraNCOQueryApiRequestBody = {
      startRow: 0,
      endRow: 100,
      eventFilters: [
        {
          event: OneVmsEventKey.INTEGRATION_TEST,
          negated: false, // Positive filter
        },
      ],
    };

    const event = createApiGwEvent({
      userOrgId: importerOrgId,
      kasApplications: appsWithVisibility,
      body: requestBody as unknown as Record<string, unknown>,
    });

    const response = await invokeApiGwLambda(lambdaArn, event);
    console.log(response.body);
    expect(response.statusCode).toBe(200);

    const body = JSON.parse(response.body) as CoraNCOQueryApiResponse;
    expect(body.data).toBeDefined();

    // Should only return the matching order
    expect(body.data.length).toBe(1);
    expect(body.data[0].pk_new_car_order_id).toBe(matchingOrderId);
  });

  it('should return orders not matching the mapping with negative filter (negated=true)', async () => {
    // Create request body with negative event filter (negated=true)
    const requestBody: CoraNCOQueryApiRequestBody = {
      startRow: 0,
      endRow: 100,
      eventFilters: [
        {
          event: OneVmsEventKey.INTEGRATION_TEST,
          negated: true, // Negative filter
        },
      ],
    };

    const event = createApiGwEvent({
      userOrgId: importerOrgId,
      kasApplications: appsWithVisibility,
      body: requestBody as unknown as Record<string, unknown>,
    });

    const response = await invokeApiGwLambda(lambdaArn, event);
    expect(response.statusCode).toBe(200);

    const body = JSON.parse(response.body) as CoraNCOQueryApiResponse;
    expect(body.data).toBeDefined();

    // Should return orders that don't match the mapping
    expect(body.data.length).toBe(2);

    const orderIds = body.data.map((order) => order.pk_new_car_order_id);
    expect(orderIds).toContain(nonMatchingOrderId1);
    expect(orderIds).toContain(nonMatchingOrderId2);
    expect(orderIds).not.toContain(matchingOrderId);
  });

  it('should handle multiple event filters correctly', async () => {
    // Create request body with both positive and negative filters for the same event
    const requestBody: CoraNCOQueryApiRequestBody = {
      startRow: 0,
      endRow: 100,
      eventFilters: [
        {
          event: OneVmsEventKey.INTEGRATION_TEST,
          negated: false, // Positive filter
        },
        {
          event: OneVmsEventKey.INTEGRATION_TEST,
          negated: true, // Negative filter
        },
      ],
    };

    const event = createApiGwEvent({
      userOrgId: importerOrgId,
      kasApplications: appsWithVisibility,
      body: requestBody as unknown as Record<string, unknown>,
    });

    const response = await invokeApiGwLambda(lambdaArn, event);
    expect(response.statusCode).toBe(200);

    const body = JSON.parse(response.body) as CoraNCOQueryApiResponse;
    expect(body.data).toBeDefined();

    // This combination should return no orders (matching positive AND matching negative is impossible)
    expect(body.data.length).toBe(0);
  });

  it('should handle empty event filters', async () => {
    // Create request body with no event filters
    const requestBody: CoraNCOQueryApiRequestBody = {
      startRow: 0,
      endRow: 100,
      eventFilters: [],
    };

    const event = createApiGwEvent({
      userOrgId: importerOrgId,
      kasApplications: appsWithVisibility,
      body: requestBody as unknown as Record<string, unknown>,
    });

    const response = await invokeApiGwLambda(lambdaArn, event);
    expect(response.statusCode).toBe(200);

    const body = JSON.parse(response.body) as CoraNCOQueryApiResponse;
    expect(body.data).toBeDefined();

    // Should return all orders when no filters are applied
    expect(body.data.length).toBe(3);
  });
});
