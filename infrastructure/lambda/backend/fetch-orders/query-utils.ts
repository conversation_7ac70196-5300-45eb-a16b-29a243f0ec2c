import { Brackets, SelectQuery<PERSON>uilder, WhereExpressionBuilder } from 'typeorm';
import { CoraMdOneVmsStatus } from '../../../lib/types/masterdata-types';
import { CoraNCOBaseApiResponse, FilterModelRequest } from '../../../lib/types/new-car-order-types';
import { Kas<PERSON>ambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { FilterModel, SetFilterModel, TextFilterModel } from '../../../lib/types/ag-grid-ssrm-types';
import { NewCarOrderModel } from '../../../lib/entities/new-car-order-model';
import { SpecialStatusCode } from '../../../lib/types/process-steering-types';
import { InboundProcessMappingModel } from '../../../lib/entities/inbound-mapping-model';

function _getStatusQuery(
  status: CoraMdOneVmsStatus,
  index: number,
): { queryString: string; queryParams: Record<string, string | null> } {
  return {
    queryString: `new_car_order.order_status_onevms_code = :statusCode${index} AND new_car_order.order_status_onevms_error_code = :errorCode${index}`,
    queryParams: {
      [`statusCode${index}`]: status.one_vms_status,
      [`errorCode${index}`]: status.one_vms_error_status,
    },
  };
}
export function getStatusWhereQuery(statusCodesForQuery: CoraMdOneVmsStatus[]): Brackets {
  return new Brackets((qb) => {
    statusCodesForQuery.forEach((status, index) => {
      const _query = _getStatusQuery(status, index);
      if (index === 0) {
        //find all orders that have the exact combination of statusCode and errorCode. Because we have multiple, we need a forEach loop
        //index === 0 is the first iteration, so we use where instead of orWhere
        qb.where(_query.queryString, _query.queryParams);
      } else {
        //index > 0 is the following iterations, so we use orWhere
        qb.orWhere(_query.queryString, _query.queryParams);
      }
    });
  });
}

function agGridSsrmCreateTextFilterSql(
  queryBuilder: SelectQueryBuilder<NewCarOrderModel> | WhereExpressionBuilder,
  key: keyof CoraNCOBaseApiResponse,
  filter: TextFilterModel,
  useOrWhere: boolean = false,
  index: number = 0,
): void {
  switch (filter.type) {
    case 'equals':
      if (useOrWhere) {
        queryBuilder.orWhere(`new_car_order.${key} = :${key}${index}`, { [`${key}${index}`]: filter.filter });
      } else {
        queryBuilder.andWhere(`new_car_order.${key} = :${key}${index}`, { [`${key}${index}`]: filter.filter });
      }
      break;
    case 'notEqual':
      if (useOrWhere) {
        queryBuilder.orWhere(`new_car_order.${key} != :${key}${index}`, { [`${key}${index}`]: filter.filter });
      } else {
        queryBuilder.andWhere(`new_car_order.${key} != :${key}${index}`, { [`${key}${index}`]: filter.filter });
      }
      break;
    case 'contains':
      if (useOrWhere) {
        queryBuilder.orWhere(`new_car_order.${key} ILIKE :${key}${index}`, {
          [`${key}${index}`]: `%${filter.filter}%`,
        });
      } else {
        queryBuilder.andWhere(`new_car_order.${key} ILIKE :${key}${index}`, {
          [`${key}${index}`]: `%${filter.filter}%`,
        });
      }
      break;
    case 'notContains':
      if (useOrWhere) {
        queryBuilder.orWhere(`new_car_order.${key} NOT ILIKE :${key}${index}`, {
          [`${key}${index}`]: `%${filter.filter}%`,
        });
      } else {
        queryBuilder.andWhere(`new_car_order.${key} NOT ILIKE :${key}${index}`, {
          [`${key}${index}`]: `%${filter.filter}%`,
        });
      }
      break;
    case 'startsWith':
      if (useOrWhere) {
        queryBuilder.orWhere(`new_car_order.${key} ILIKE :${key}${index}`, { [`${key}${index}`]: `%${filter.filter}` });
      } else {
        queryBuilder.andWhere(`new_car_order.${key} ILIKE :${key}${index}`, {
          [`${key}${index}`]: `%${filter.filter}`,
        });
      }
      break;
    case 'endsWith':
      if (useOrWhere) {
        queryBuilder.orWhere(`new_car_order.${key} ILIKE :${key}${index}`, { [`${key}${index}`]: `${filter.filter}%` });
      } else {
        queryBuilder.andWhere(`new_car_order.${key} ILIKE :${key}${index}`, {
          [`${key}${index}`]: `${filter.filter}%`,
        });
      }
  }
}

function agGridSsrmCreateSetFilterSql(
  queryBuilder: SelectQueryBuilder<NewCarOrderModel>,
  key: keyof CoraNCOBaseApiResponse,
  filter: SetFilterModel,
): void {
  queryBuilder.andWhere(`new_car_order.${key} IN (:...${key}s)`, { [`${key}s`]: filter.values });
}

export function agGridSsrmCreateWhereSql(
  queryBuilder: SelectQueryBuilder<NewCarOrderModel>,
  filterModel: FilterModelRequest,
  logger: KasLambdaLogger,
): void {
  const keySet = Object.entries(filterModel) as [keyof CoraNCOBaseApiResponse, FilterModel][];
  for (const [k, v] of keySet) {
    if ('operator' in v) {
      // JoinFilterModel
      queryBuilder.andWhere(
        new Brackets((qb) => {
          let index = 0;
          for (const condition of v.conditions) {
            agGridSsrmCreateTextFilterSql(qb, k, condition, v.operator === 'OR', index);
            index++;
          }
        }),
      );
    } else if (v.filterType === 'text') {
      // TextFilterModel
      agGridSsrmCreateTextFilterSql(queryBuilder, k, v);
      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    } else if (v.filterType === 'set') {
      // SetFilterModel
      agGridSsrmCreateSetFilterSql(queryBuilder, k, v);
    } else {
      logger.log(LogLevel.ERROR, 'Unknown filter type', { data: v });
    }
  }
}

export function hasEmptySetFilter(filterModel?: FilterModelRequest): boolean {
  if (!filterModel) return false;

  for (const v of Object.values(filterModel)) {
    if (
      'filterType' in v &&
      v.filterType === 'set' &&
      'values' in v &&
      Array.isArray(v.values) &&
      v.values.length === 0
    ) {
      return true;
    }
  }
  return false;
}

export function applyInboundMappingFilterPositive(
  queryBuilder: SelectQueryBuilder<NewCarOrderModel>,
  mappings: InboundProcessMappingModel[],
  logger: KasLambdaLogger,
): void {
  if (mappings.length === 0) {
    logger.log(LogLevel.DEBUG, 'InboundFilter (positive): No mappings found. Skipping filter.');
    return;
  }

  const sanitizeParamKey = (base: string, index: number): string => `${base}_${index}`;

  const filterBrackets = new Brackets((qbOuter) => {
    mappings.forEach((mapping, index) => {
      qbOuter.orWhere(
        new Brackets((qbInner) => {
          // Order Status
          if (mapping.order_status_code !== SpecialStatusCode.ALL.toString()) {
            if (mapping.order_status_code === SpecialStatusCode.NONE.toString()) {
              qbInner.andWhere("new_car_order.order_status_onevms_code = 'null'");
            } else {
              const key = sanitizeParamKey('orderStatus', index);
              const op = mapping.order_status_code.endsWith('*') ? 'LIKE' : '=';
              const value = mapping.order_status_code.endsWith('*')
                ? `${mapping.order_status_code.slice(0, -1)}%`
                : mapping.order_status_code;
              qbInner.andWhere(`new_car_order.order_status_onevms_code ${op} :${key}`, { [key]: value });
            }
          }

          // Error Status
          if (mapping.error_status_code !== SpecialStatusCode.ALL.toString()) {
            if (mapping.error_status_code === SpecialStatusCode.NONE.toString()) {
              qbInner.andWhere("new_car_order.order_status_onevms_error_code  = 'null'");
            } else {
              const key = sanitizeParamKey('errorStatus', index);
              const op = mapping.error_status_code.endsWith('*') ? 'LIKE' : '=';
              const value = mapping.error_status_code.endsWith('*')
                ? `${mapping.error_status_code.slice(0, -1)}%`
                : mapping.error_status_code;
              qbInner.andWhere(`new_car_order.order_status_onevms_error_code ${op} :${key}`, { [key]: value });
            }
          }

          // Invoice Status
          if (mapping.invoice_status_code !== SpecialStatusCode.ALL.toString()) {
            if (mapping.invoice_status_code === SpecialStatusCode.NONE.toString()) {
              qbInner.andWhere("new_car_order.order_invoice_onevms_code = 'null'");
            } else {
              const key = sanitizeParamKey('invoiceStatus', index);
              const op = mapping.invoice_status_code.endsWith('*') ? 'LIKE' : '=';
              const value = mapping.invoice_status_code.endsWith('*')
                ? `${mapping.invoice_status_code.slice(0, -1)}%`
                : mapping.invoice_status_code;
              qbInner.andWhere(`new_car_order.order_invoice_onevms_code ${op} :${key}`, { [key]: value });
            }
          }
        }),
      );
    });
  });

  queryBuilder.andWhere(filterBrackets);
  logger.log(LogLevel.DEBUG, 'InboundFilter (positive) applied.');
}

export function applyInboundMappingFilterNegative(
  queryBuilder: SelectQueryBuilder<NewCarOrderModel>,
  mappings: InboundProcessMappingModel[],
  logger: KasLambdaLogger,
): void {
  if (mappings.length === 0) {
    logger.log(LogLevel.DEBUG, 'InboundFilter (negative): No mappings found. Skipping filter.');
    return;
  }

  const sanitizeParamKey = (base: string, index: number): string => `${base}_${index}`;

  const filterBrackets = new Brackets((qbOuter) => {
    mappings.forEach((mapping, index) => {
      qbOuter.andWhere(
        new Brackets((qbInner) => {
          // negate the positive clause for each mapping

          // Order Status
          if (mapping.order_status_code !== SpecialStatusCode.ALL.toString()) {
            if (mapping.order_status_code === SpecialStatusCode.NONE.toString()) {
              qbInner.orWhere(
                new Brackets((qb) => {
                  qb.where('new_car_order.order_status_onevms_code IS NOT NULL').andWhere(
                    'new_car_order.order_status_onevms_code != :nullStr',
                    { nullStr: 'null' },
                  );
                }),
              );
            } else {
              const key = sanitizeParamKey('orderStatus', index);
              const op = mapping.order_status_code.endsWith('*') ? 'NOT LIKE' : '!=';
              const value = mapping.order_status_code.endsWith('*')
                ? `${mapping.order_status_code.slice(0, -1)}%`
                : mapping.order_status_code;
              qbInner.orWhere(`new_car_order.order_status_onevms_code ${op} :${key}`, { [key]: value });
            }
          }

          // Error Status
          if (mapping.error_status_code !== SpecialStatusCode.ALL.toString()) {
            if (mapping.error_status_code === SpecialStatusCode.NONE.toString()) {
              qbInner.orWhere(
                new Brackets((qb) => {
                  qb.where('new_car_order.order_status_onevms_error_code IS NOT NULL').andWhere(
                    'new_car_order.order_status_onevms_error_code != :nullStr',
                    { nullStr: 'null' },
                  );
                }),
              );
            } else {
              const key = sanitizeParamKey('errorStatus', index);
              const op = mapping.error_status_code.endsWith('*') ? 'NOT LIKE' : '!=';
              const value = mapping.error_status_code.endsWith('*')
                ? `${mapping.error_status_code.slice(0, -1)}%`
                : mapping.error_status_code;
              qbInner.orWhere(`new_car_order.order_status_onevms_error_code ${op} :${key}`, { [key]: value });
            }
          }

          // Invoice Status
          if (mapping.invoice_status_code !== SpecialStatusCode.ALL.toString()) {
            if (mapping.invoice_status_code === SpecialStatusCode.NONE.toString()) {
              qbInner.orWhere('new_car_order.order_invoice_onevms_code IS NOT NULL');
              qbInner.orWhere(
                new Brackets((qb) => {
                  qb.where('new_car_order.order_invoice_onevms_code IS NOT NULL').andWhere(
                    'new_car_order.order_invoice_onevms_code != :nullStr',
                    { nullStr: 'null' },
                  );
                }),
              );
            } else {
              const key = sanitizeParamKey('invoiceStatus', index);
              const op = mapping.invoice_status_code.endsWith('*') ? 'NOT LIKE' : '!=';
              const value = mapping.invoice_status_code.endsWith('*')
                ? `${mapping.invoice_status_code.slice(0, -1)}%`
                : mapping.invoice_status_code;
              qbInner.orWhere(`new_car_order.order_invoice_onevms_code ${op} :${key}`, { [key]: value });
            }
          }
        }),
      );
    });
  });

  queryBuilder.andWhere(filterBrackets);
  logger.log(LogLevel.DEBUG, 'InboundFilter (negative, direct logic) applied.');
}
