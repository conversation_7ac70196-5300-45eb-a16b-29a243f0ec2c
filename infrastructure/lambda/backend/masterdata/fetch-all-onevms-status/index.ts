import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { sendFail, sendSuccess } from '../../../utils/api-helpers';
import { getEnvVarWithAssert } from '../../../utils/utils';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { createApiGwHandler } from '../../../utils/api-gw-handler';
import { OneVmsStatusModel } from '../../../../lib/entities/onevms-status-model';
import { createTypeORMDataSource } from '../../../config/typeorm-config';
import { secretCache } from '../../../utils/secret-cache';

const AURORA_SECRET_ARN = getEnvVarWithAssert('AURORA_SECRET_ARN');
const STAGE = getEnvVarWithAssert('STAGE');
secretCache.initCache(AURORA_SECRET_ARN);
const fetchAllOneVMSStatusFuc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  try {
    const dataSource = await createTypeORMDataSource(logger, AURORA_SECRET_ARN, STAGE, [OneVmsStatusModel]);
    const statusCodes = await dataSource
      .getRepository(OneVmsStatusModel)
      .createQueryBuilder('status')
      .where('status.is_deactivated IS NULL OR status.is_deactivated = false')
      .getMany();

    logger.log(LogLevel.DEBUG, `Got active statusCodes`, { data: statusCodes });

    return sendSuccess({ body: statusCodes, reqHeaders: event.headers }, logger);
  } catch (err) {
    logger.log(LogLevel.ERROR, 'Error getting onevms status', { data: err });
    return sendFail({ message: 'Error loading onevms status', status: 500, reqHeaders: event.headers }, logger);
  }
};

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGwHandler('get-onevms-status', LogLevel.TRACE)(event, context, fetchAllOneVMSStatusFuc);
