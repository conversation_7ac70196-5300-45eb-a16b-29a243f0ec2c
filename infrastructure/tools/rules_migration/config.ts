// Configuration for different environments
export const envConfigs = {
  INT: {
    resourceArn: 'arn:aws:rds:eu-west-1:631606382638:cluster:cora-aurora-serverless-cluster',
    secretArn: 'arn:aws:secretsmanager:eu-west-1:631606382638:secret:cora-aurora-admin-secret-yzqZhq',
  },
  PROD: {
    resourceArn: 'arn:aws:rds:eu-west-1:631606382638:cluster:cora-aurora-serverless-cluster',
    secretArn: 'arn:aws:secretsmanager:eu-west-1:631606382638:secret:cora-aurora-admin-secret-yzqZhq',
  },
};

// Database configuration
export const dbConfig = {
  database: 'coraDB',
  schema: 'masterdata',
};

// Backup file paths for each table
export function getFilePaths(tableName: string) {
  const backupDir = __dirname + '/backups';
  return {
    backupFile: `${backupDir}/${tableName}_backup.json`,
    backupDir: backupDir,
  };
}
