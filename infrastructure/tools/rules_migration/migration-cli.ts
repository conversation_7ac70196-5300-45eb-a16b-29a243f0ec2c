#!/usr/bin/env node

import { DataSource } from 'typeorm';
import { InboundProcessMappingModel } from '../../lib/entities/inbound-mapping-model';
import { OutboundProcessMappingModel } from '../../lib/entities/outbound-mapping-model';
import { OneVmsStatusModel } from '../../lib/entities/onevms-status-model';
import { envConfigs, dbConfig } from './config';
import * as fs from 'fs';
import * as path from 'path';

import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';

// Types
type Environment = 'INT' | 'PROD';
type TableName = 'inbound_process_rules' | 'outbound_process_rules' | 'onevms_status';

interface BackupData {
  exportedAt: string;
  source: Environment;
  tableName: TableName;
  recordCount: number;
  records: any[];
}

// Table configuration
const tableConfig = {
  inbound_process_rules: {
    entity: InboundProcessMappingModel,
    displayName: 'Inbound Process Rules',
    description: 'Migration rules for inbound processes',
  },
  outbound_process_rules: {
    entity: OutboundProcessMappingModel,
    displayName: 'Outbound Process Rules',
    description: 'Migration rules for outbound processes',
  },
  onevms_status: {
    entity: OneVmsStatusModel,
    displayName: 'OneVMS Status',
    description: 'Status definitions for OneVMS system',
  },
};

// Utility functions
function createDataSource(environment: Environment): DataSource {
  const config = envConfigs[environment];

  return new DataSource({
    type: 'aurora-postgres',
    secretArn: config.secretArn,
    resourceArn: config.resourceArn,
    region: 'eu-west-1',
    database: dbConfig.database,
    entities: [InboundProcessMappingModel, OutboundProcessMappingModel, OneVmsStatusModel],
    synchronize: false,
    logging: false,
  });
}

function getBackupFilePath(tableName: TableName): string {
  const backupDir = path.join(__dirname, 'backups');
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  return path.join(backupDir, `${tableName}_backup.json`);
}

function saveBackup(tableName: TableName, records: any[], source: Environment): void {
  const backupData: BackupData = {
    exportedAt: new Date().toISOString(),
    source,
    tableName,
    recordCount: records.length,
    records: records.map((record) => {
      const plainRecord = { ...record };

      // Convert dates to ISO strings
      if (plainRecord.created_at instanceof Date) {
        plainRecord.created_at = plainRecord.created_at.toISOString();
      }
      if (plainRecord.modified_at instanceof Date) {
        plainRecord.modified_at = plainRecord.modified_at.toISOString();
      }

      // Ensure created_by and modified_by are preserved
      if (!plainRecord.created_by) {
        plainRecord.created_by = 'unknown';
      }
      if (!plainRecord.modified_by) {
        plainRecord.modified_by = 'unknown';
      }

      return plainRecord;
    }),
  };

  const filePath = getBackupFilePath(tableName);
  fs.writeFileSync(filePath, JSON.stringify(backupData, null, 2), 'utf8');
}

function loadBackup(tableName: TableName): BackupData | null {
  const filePath = getBackupFilePath(tableName);

  if (!fs.existsSync(filePath)) {
    return null;
  }

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(chalk.red(`Error reading backup file: ${error}`));
    return null;
  }
}

// CLI Menu functions
async function selectTable(): Promise<TableName> {
  const choices = Object.entries(tableConfig).map(([key, config]) => ({
    name: `📋 ${config.displayName}`,
    value: key as TableName,
    description: config.description,
  }));

  const { tableName } = await inquirer.prompt([
    {
      type: 'list',
      name: 'tableName',
      message: 'Which table do you want to work with?',
      choices,
      pageSize: 10,
    },
  ]);

  return tableName;
}

async function confirmAction(message: string, defaultValue: boolean = false): Promise<boolean> {
  const { confirmed } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'confirmed',
      message,
      default: defaultValue,
    },
  ]);

  return confirmed;
}

// Main functions
async function exportFromINT(tableName: TableName): Promise<void> {
  const spinner = ora(`Connecting to INT database for ${tableName}...`).start();
  let dataSource: DataSource | null = null;

  try {
    // Initialize connection
    dataSource = createDataSource('INT');
    await dataSource.initialize();

    const EntityClass = tableConfig[tableName].entity;
    const repository = dataSource.getRepository(EntityClass);

    spinner.text = `Fetching data from INT database table ${tableName}...`;

    // Fetch all records
    const records = await repository.find({
      order: {
        created_at: 'ASC',
      },
    });

    if (records.length === 0) {
      spinner.warn(chalk.yellow(`No data found in table ${tableName}`));
      return;
    }

    spinner.text = `Saving ${records.length} records to backup file...`;

    // Save to backup
    saveBackup(tableName, records, 'INT');

    spinner.succeed(chalk.green(`Successfully exported ${records.length} records from ${tableName}`));

    console.log(chalk.blue(`\n📊 Export Summary:`));
    console.log(chalk.white(`   • Table: ${tableConfig[tableName].displayName}`));
    console.log(chalk.white(`   • Records: ${records.length}`));
    console.log(chalk.white(`   • Source: INT environment`));
    console.log(chalk.white(`   • File: ${getBackupFilePath(tableName)}`));
  } catch (error) {
    spinner.fail(chalk.red(`Failed to export data from ${tableName}`));
    console.error(chalk.red('Error details:'), error);
    throw error;
  } finally {
    if (dataSource?.isInitialized) {
      await dataSource.destroy();
    }
  }
}

async function importToPROD(tableName: TableName): Promise<void> {
  const spinner = ora('Checking backup file...').start();
  let dataSource: DataSource | null = null;

  try {
    // Load backup
    const backupData = loadBackup(tableName);

    if (!backupData) {
      spinner.fail(chalk.red(`No backup file found for ${tableName}`));
      console.log(chalk.yellow('Please run export first to create a backup.'));
      return;
    }

    if (backupData.tableName !== tableName) {
      spinner.fail(chalk.red(`Backup file is for ${backupData.tableName}, not ${tableName}`));
      return;
    }

    if (backupData.records.length === 0) {
      spinner.fail(chalk.red('No records found in backup file'));
      return;
    }

    // Show backup info
    spinner.stop();
    console.log(chalk.blue(`\n📁 Backup Information:`));
    console.log(chalk.white(`   • Table: ${tableConfig[tableName].displayName}`));
    console.log(chalk.white(`   • Records: ${backupData.recordCount}`));
    console.log(chalk.white(`   • Exported: ${new Date(backupData.exportedAt).toLocaleString()}`));
    console.log(chalk.white(`   • Source: ${backupData.source} environment`));

    // Initialize PROD connection
    spinner.start('Connecting to PROD database...');
    dataSource = createDataSource('PROD');
    await dataSource.initialize();

    const EntityClass = tableConfig[tableName].entity;
    const repository = dataSource.getRepository(EntityClass);

    // Check existing records
    const existingCount = await repository.count();

    if (existingCount > 0) {
      spinner.stop();
      console.log(chalk.red(`\n⚠️  WARNING: ${existingCount} existing records found in PROD!`));
      console.log(chalk.yellow('This operation will DELETE ALL existing data before importing.'));

      const confirmDelete = await confirmAction(
        `Delete ${existingCount} existing records and import ${backupData.recordCount} new records?`,
        false,
      );

      if (!confirmDelete) {
        console.log(chalk.yellow('Import cancelled by user.'));
        return;
      }

      spinner.start(`Clearing ${existingCount} existing records...`);
      await repository.clear();
    }

    // Import records
    spinner.text = `Importing ${backupData.recordCount} records to PROD...`;

    const entities = backupData.records.map((record) => {
      const entity = repository.create(record as any);

      // Set current timestamp for created_at and modified_at (fresh import)
      const now = new Date();
      (entity as any).created_at = now;
      (entity as any).modified_at = now;
      (entity as any).created_by = 'migration-tool';
      (entity as any).modified_by = 'migration-tool';

      // Remove ID to let TypeORM generate new UUIDs
      delete (entity as any).id;

      return entity;
    });

    // Save in chunks
    const chunkSize = 100;
    for (let i = 0; i < entities.length; i += chunkSize) {
      const chunk = entities.slice(i, i + chunkSize);
      await repository.save(chunk as any);

      spinner.text = `Importing records ${i + 1}-${Math.min(i + chunkSize, entities.length)} of ${entities.length}...`;
    }

    spinner.succeed(chalk.green(`Successfully imported ${backupData.recordCount} records to PROD`));

    console.log(chalk.blue(`\n📊 Import Summary:`));
    console.log(chalk.white(`   • Table: ${tableConfig[tableName].displayName}`));
    console.log(chalk.white(`   • Records imported: ${backupData.recordCount}`));
    console.log(chalk.white(`   • Target: PROD environment`));
    console.log(chalk.white(`   • Status: Complete`));
  } catch (error) {
    spinner.fail(chalk.red(`Failed to import data to ${tableName}`));
    console.error(chalk.red('Error details:'), error);
    throw error;
  } finally {
    if (dataSource?.isInitialized) {
      await dataSource.destroy();
    }
  }
}

async function showBackupStatus(): Promise<void> {
  console.log(chalk.blue('\n📁 Backup Status:\n'));

  for (const [tableName, config] of Object.entries(tableConfig)) {
    const backupData = loadBackup(tableName as TableName);

    if (backupData) {
      console.log(chalk.green(`✅ ${config.displayName}`));
      console.log(chalk.white(`   • Records: ${backupData.recordCount}`));
      console.log(chalk.white(`   • Exported: ${new Date(backupData.exportedAt).toLocaleString()}`));
      console.log(chalk.white(`   • Source: ${backupData.source}`));
    } else {
      console.log(chalk.red(`❌ ${config.displayName}`));
      console.log(chalk.gray(`   • No backup available`));
    }
    console.log();
  }
}

// Main CLI
async function main(): Promise<void> {
  console.log(chalk.blue('🚀 CORA Database Migration Tool\n'));

  while (true) {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'What would you like to do?',
        choices: [
          { name: '📤 Export data from INT', value: 'export' },
          { name: '📥 Import data to PROD', value: 'import' },
          { name: '📋 Show backup status', value: 'status' },
          { name: '🚪 Exit', value: 'exit' },
        ],
        pageSize: 10,
      },
    ]);

    try {
      switch (action) {
        case 'export': {
          const tableName = await selectTable();

          console.log(chalk.blue(`\n🚀 Starting export for ${tableConfig[tableName].displayName}...`));

          const confirmExport = await confirmAction(`Export all data from ${tableName} in INT environment?`, true);

          if (confirmExport) {
            await exportFromINT(tableName);
          } else {
            console.log(chalk.yellow('Export cancelled.'));
          }
          break;
        }

        case 'import': {
          const tableName = await selectTable();

          console.log(chalk.blue(`\n🚀 Starting import for ${tableConfig[tableName].displayName}...`));
          console.log(chalk.red('⚠️  WARNING: This will REPLACE all existing data in PROD!'));

          const confirmImport = await confirmAction(`Import ${tableName} data to PROD environment?`, false);

          if (confirmImport) {
            await importToPROD(tableName);
          } else {
            console.log(chalk.yellow('Import cancelled.'));
          }
          break;
        }

        case 'status':
          await showBackupStatus();
          break;

        case 'exit':
          console.log(chalk.green('\n👋 Goodbye!'));
          process.exit(0);
      }
    } catch (error) {
      console.error(chalk.red('\n❌ Operation failed:'), error);
      console.log(chalk.yellow('\nPlease check your database configuration and try again.\n'));
    }
  }
}

// Error handling
process.on('uncaughtException', (error) => {
  console.error(chalk.red('Fatal error:'), error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('Unhandled rejection at:'), promise, chalk.red('reason:'), reason);
  process.exit(1);
});

// Start the application
main().catch(console.error);
