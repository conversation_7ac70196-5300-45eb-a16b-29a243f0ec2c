# CORA Database Migration Tool

A CLI tool for migrating database tables between Aurora PostgreSQL environments (INT ➝ PROD) using TypeORM and AWS RDS Data API.

## Features

- **Multi-table Support**: Migrate `inbound_process_rules`, `outbound_process_rules`, and `onevms_status`
- **Interactive CLI**: User-friendly interface with table selection and safety confirmations
- **TypeORM Integration**: Uses existing entity models for type safety
- **JSON Backups**: Export/import via JSON files with metadata
- **Fresh Data**: Generates new UUIDs and current timestamps on import
- **Safety**: Confirmation prompts for destructive operations

## Usage

```bash
cd infrastructure
npm run migrate:rules
```

### Operations

1. **Export from INT**: Select table → Export to JSON backup file
2. **Import to PROD**: Select table → Import from backup with safety confirmations
3. **Show Status**: Overview of all backup files

## Configuration

Update ARNs in `config.ts`:

```typescript
export const envConfigs = {
  INT: {
    resourceArn: 'arn:aws:rds:eu-west-1:...',
    secretArn: 'arn:aws:secretsmanager:eu-west-1:...',
  },
  PROD: {
    resourceArn: 'arn:aws:rds:eu-west-1:...',
    secretArn: 'arn:aws:secretsmanager:eu-west-1:...',
  },
};
```

## Files

```
tools/rules_migration/
├── migration-cli.ts    # Main CLI application
├── config.ts           # Environment configuration
├── backups/            # JSON backup files
└── README.md           # This file
```

## Safety Features

- Double confirmation for PROD imports
- Clear warnings for destructive operations
- Backup validation before import
- New UUIDs prevent ID conflicts
- Progress indicators and error handling
