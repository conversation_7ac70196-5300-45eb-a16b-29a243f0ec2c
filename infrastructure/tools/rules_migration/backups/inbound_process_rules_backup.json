{"exportedAt": "2025-07-25T13:33:27.795Z", "source": "INT", "tableName": "inbound_process_rules", "recordCount": 53, "records": [{"created_at": "2025-04-14T09:22:14.817Z", "created_by": "ffyerqw", "modified_at": "2025-05-21T14:11:02.496Z", "modified_by": "ffyerqw", "id": "cc63d4f4-3754-4c61-adff-2bbcc87d7d43", "order_status_code": "PP*", "error_status_code": "*", "invoice_status_code": "*", "event": "update-core-data", "source_system": "cora_user", "target_event_handler": "update-nco-core"}, {"created_at": "2025-04-29T08:13:38.639Z", "created_by": "ffyerqw", "modified_at": "2025-05-21T14:11:02.496Z", "modified_by": "ffyerqw", "id": "c2f95d2f-db4d-4513-a449-ccc07992d22b", "order_status_code": "IP*", "error_status_code": "*", "invoice_status_code": "*", "event": "update-core-data", "source_system": "cora_user", "target_event_handler": "update-nco-core"}, {"created_at": "2025-05-19T11:28:38.836Z", "created_by": "ffyerqw", "modified_at": "2025-05-19T11:28:38.836Z", "modified_by": "ffyerqw", "id": "b30792d8-2b39-443e-8a0c-453308b19496", "order_status_code": "PP1300", "error_status_code": "FBP500", "invoice_status_code": "*", "event": "cancel-scheduled", "source_system": "cora_system", "target_event_handler": "cancel-nco-scheduled"}, {"created_at": "2025-05-20T16:11:52.021Z", "created_by": "p358886", "modified_at": "2025-05-21T14:11:02.496Z", "modified_by": "ffyerqw", "id": "79200332-d257-4045-a78d-ea60aaaf38cb", "order_status_code": "ID*", "error_status_code": "*", "invoice_status_code": "*", "event": "update-core-data", "source_system": "cora_user", "target_event_handler": "update-nco"}, {"created_at": "2025-05-21T06:02:56.631Z", "created_by": "ffyerqw", "modified_at": "2025-05-21T06:02:56.631Z", "modified_by": "ffyerqw", "id": "97bce622-e820-450d-bb36-d6e181e753ab", "order_status_code": "null", "error_status_code": "null", "invoice_status_code": "null", "event": "create", "source_system": "cora_user", "target_event_handler": "create-nco"}, {"created_at": "2025-05-21T11:09:17.636Z", "created_by": "ffyerqw", "modified_at": "2025-05-27T06:36:12.614Z", "modified_by": "ffyerqw", "id": "6438845d-6ab8-4f0a-a015-dcce5b701cdd", "order_status_code": "null", "error_status_code": "null", "invoice_status_code": "null", "event": "convert-pi", "source_system": "cora_user", "target_event_handler": "convert-pi"}, {"created_at": "2025-05-21T14:13:33.022Z", "created_by": "ffyerqw", "modified_at": "2025-05-21T14:14:18.778Z", "modified_by": "ffyerqw", "id": "58a7acf7-5ff5-444f-9a14-e2d29b4a04fd", "order_status_code": "OX1100", "error_status_code": "null", "invoice_status_code": "*", "event": "total-loss-persist", "source_system": "cora_system", "target_event_handler": "total-loss-persist"}, {"created_at": "2025-05-21T14:13:33.022Z", "created_by": "ffyerqw", "modified_at": "2025-05-21T14:13:33.022Z", "modified_by": "ffyerqw", "id": "d25bff30-070c-443e-b420-8b77e6922e75", "order_status_code": "IP*", "error_status_code": "*", "invoice_status_code": "*", "event": "total-loss-report", "source_system": "cora_user", "target_event_handler": "total-loss-report"}, {"created_at": "2025-05-21T14:13:33.022Z", "created_by": "ffyerqw", "modified_at": "2025-05-23T09:01:19.990Z", "modified_by": "ffyerqw", "id": "734a086f-9d37-41f1-9ae1-a086c912a6a6", "order_status_code": "ID*", "error_status_code": "*", "invoice_status_code": "*", "event": "total-loss-report", "source_system": "cora_user", "target_event_handler": "total-loss-report"}, {"created_at": "2025-05-21T14:13:33.022Z", "created_by": "ffyerqw", "modified_at": "2025-05-21T14:13:33.022Z", "modified_by": "ffyerqw", "id": "ac02aa9b-3faa-454c-b2d2-362dcdff73ea", "order_status_code": "OX1100", "error_status_code": "null", "invoice_status_code": "*", "event": "total-loss-revoke", "source_system": "cora_user", "target_event_handler": "total-loss-revoke"}, {"created_at": "2025-05-22T12:43:08.019Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:43:08.019Z", "modified_by": "ffyerqw", "id": "975f7029-ba9a-46ce-a35b-2fc168f06e85", "order_status_code": "PP1300", "error_status_code": "FBP500", "invoice_status_code": "*", "event": "update-data", "source_system": "cora_user", "target_event_handler": "update-nco"}, {"created_at": "2025-05-22T12:43:08.019Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:43:08.019Z", "modified_by": "ffyerqw", "id": "e39aec4c-da63-43a8-a699-02070d6e6c27", "order_status_code": "PP1300", "error_status_code": "FBP920", "invoice_status_code": "*", "event": "update-data", "source_system": "cora_user", "target_event_handler": "update-nco"}, {"created_at": "2025-05-22T12:43:08.019Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:43:08.019Z", "modified_by": "ffyerqw", "id": "a6cc677c-adbe-4044-a6b4-c00d5aa51763", "order_status_code": "PP1300", "error_status_code": "LBP200", "invoice_status_code": "*", "event": "update-data", "source_system": "cora_user", "target_event_handler": "update-nco"}, {"created_at": "2025-05-22T12:43:08.019Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:43:08.019Z", "modified_by": "ffyerqw", "id": "2b5b7e7b-e7fb-4150-803c-477cf88231ef", "order_status_code": "PP2000", "error_status_code": "null", "invoice_status_code": "*", "event": "update-data", "source_system": "cora_user", "target_event_handler": "update-nco"}, {"created_at": "2025-05-22T12:43:08.019Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:43:08.019Z", "modified_by": "ffyerqw", "id": "06913edf-5262-4806-84bf-b658a316d72f", "order_status_code": "PP2000", "error_status_code": "LBP200", "invoice_status_code": "*", "event": "update-data", "source_system": "cora_user", "target_event_handler": "update-nco"}, {"created_at": "2025-05-22T12:43:08.019Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:43:08.019Z", "modified_by": "ffyerqw", "id": "b696c694-91a0-4eff-9cb2-498b0e4aa136", "order_status_code": "PP0000", "error_status_code": "null", "invoice_status_code": "*", "event": "update-data", "source_system": "cora_user", "target_event_handler": "update-nco"}, {"created_at": "2025-05-22T12:43:08.019Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:43:08.019Z", "modified_by": "ffyerqw", "id": "ae413e3b-e6cb-4571-bb97-2dac9ede0b4b", "order_status_code": "PP0000", "error_status_code": "LBP200", "invoice_status_code": "*", "event": "update-data", "source_system": "cora_user", "target_event_handler": "update-nco"}, {"created_at": "2025-05-22T12:43:08.019Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:43:08.019Z", "modified_by": "ffyerqw", "id": "8549bf75-7438-4e8f-8e7c-af10310b631b", "order_status_code": "PP2200", "error_status_code": "null", "invoice_status_code": "*", "event": "update-data", "source_system": "cora_user", "target_event_handler": "update-nco"}, {"created_at": "2025-05-22T12:43:08.019Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:43:08.019Z", "modified_by": "ffyerqw", "id": "71a6f315-e6b7-4c9c-80b3-1a2982ff40e3", "order_status_code": "PP0000", "error_status_code": "FBP100", "invoice_status_code": "*", "event": "update-data", "source_system": "cora_user", "target_event_handler": "update-nco"}, {"created_at": "2025-05-22T12:43:08.019Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:43:08.019Z", "modified_by": "ffyerqw", "id": "07e65eb4-37ae-43c5-aa4c-7ba4afa8d654", "order_status_code": "PP1300", "error_status_code": "FBP910", "invoice_status_code": "*", "event": "update-data", "source_system": "cora_user", "target_event_handler": "update-nco"}, {"created_at": "2025-05-22T12:44:59.279Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:44:59.279Z", "modified_by": "ffyerqw", "id": "bc1d14e1-e207-4a10-9932-37aca433c804", "order_status_code": "PP1100", "error_status_code": "FBP300", "invoice_status_code": "*", "event": "update-data", "source_system": "cora_user", "target_event_handler": "update-nco"}, {"created_at": "2025-05-22T12:50:50.138Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:50:50.138Z", "modified_by": "ffyerqw", "id": "49d796d9-9839-464e-ad56-3d798a5ed724", "order_status_code": "PP1100", "error_status_code": "FBP300", "invoice_status_code": "*", "event": "cancel", "source_system": "cora_user", "target_event_handler": "cancel-nco"}, {"created_at": "2025-05-22T12:50:50.138Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:50:50.138Z", "modified_by": "ffyerqw", "id": "91312d86-0319-4d5a-bc1d-05b3bdda86ca", "order_status_code": "PP0000", "error_status_code": "null", "invoice_status_code": "*", "event": "deallocate", "source_system": "cora_user", "target_event_handler": "deallocate-quota"}, {"created_at": "2025-05-22T12:50:50.138Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:50:50.138Z", "modified_by": "ffyerqw", "id": "05e8d188-a633-408f-9756-de8b17a6830b", "order_status_code": "PP0000", "error_status_code": "LBP200", "invoice_status_code": "*", "event": "deallocate", "source_system": "cora_user", "target_event_handler": "deallocate-quota"}, {"created_at": "2025-05-22T12:50:50.138Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:50:50.138Z", "modified_by": "ffyerqw", "id": "754ce720-164c-43ba-b89c-1eb8eef02ff8", "order_status_code": "PP0000", "error_status_code": "FBP100", "invoice_status_code": "*", "event": "deallocate", "source_system": "cora_user", "target_event_handler": "deallocate-quota"}, {"created_at": "2025-05-22T12:50:50.138Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:50:50.138Z", "modified_by": "ffyerqw", "id": "92330384-e822-49fc-97f6-f1176b179f46", "order_status_code": "PP1100", "error_status_code": "FBP300", "invoice_status_code": "*", "event": "deallocate", "source_system": "cora_user", "target_event_handler": "deallocate-quota"}, {"created_at": "2025-05-22T12:50:50.138Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:50:50.138Z", "modified_by": "ffyerqw", "id": "2230ddc3-e603-4a48-a1c3-02dcb495ac2f", "order_status_code": "PP1300", "error_status_code": "FBP910", "invoice_status_code": "*", "event": "deallocate", "source_system": "cora_user", "target_event_handler": "deallocate-quota"}, {"created_at": "2025-05-22T12:50:50.138Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:50:50.138Z", "modified_by": "ffyerqw", "id": "d2406057-93fc-4b43-ac85-506c482ccce5", "order_status_code": "PP1300", "error_status_code": "FBP920", "invoice_status_code": "*", "event": "deallocate", "source_system": "cora_user", "target_event_handler": "deallocate-quota"}, {"created_at": "2025-05-22T12:50:50.138Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:50:50.138Z", "modified_by": "ffyerqw", "id": "dd3f90fb-c0b9-4378-b0de-6fb586d93762", "order_status_code": "PP1300", "error_status_code": "LBP200", "invoice_status_code": "*", "event": "deallocate", "source_system": "cora_user", "target_event_handler": "deallocate-quota"}, {"created_at": "2025-05-22T12:50:50.138Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:50:50.138Z", "modified_by": "ffyerqw", "id": "2d242426-c437-4c7a-ab40-a532fe9c6a25", "order_status_code": "PP2000", "error_status_code": "null", "invoice_status_code": "*", "event": "deallocate", "source_system": "cora_user", "target_event_handler": "deallocate-quota"}, {"created_at": "2025-05-22T12:50:50.138Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:50:50.138Z", "modified_by": "ffyerqw", "id": "735c80f8-df2c-4d5a-aa0e-121b77583ad7", "order_status_code": "PP2000", "error_status_code": "LBP200", "invoice_status_code": "*", "event": "deallocate", "source_system": "cora_user", "target_event_handler": "deallocate-quota"}, {"created_at": "2025-05-22T12:50:50.138Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:50:50.138Z", "modified_by": "ffyerqw", "id": "79f00bd0-2d13-419c-b46b-3b195636631a", "order_status_code": "PP2200", "error_status_code": "null", "invoice_status_code": "*", "event": "deallocate", "source_system": "cora_user", "target_event_handler": "deallocate-quota"}, {"created_at": "2025-05-22T12:50:50.138Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:50:50.138Z", "modified_by": "ffyerqw", "id": "4bb880d9-7183-4843-9e69-256e2d12291a", "order_status_code": "PP0000", "error_status_code": "null", "invoice_status_code": "*", "event": "cancel", "source_system": "cora_user", "target_event_handler": "cancel-nco"}, {"created_at": "2025-05-22T12:50:50.138Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:50:50.138Z", "modified_by": "ffyerqw", "id": "293f818e-1867-4de5-b1ff-3be15b2782f0", "order_status_code": "PP0000", "error_status_code": "LBP200", "invoice_status_code": "*", "event": "cancel", "source_system": "cora_user", "target_event_handler": "cancel-nco"}, {"created_at": "2025-05-22T12:50:50.138Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:50:50.138Z", "modified_by": "ffyerqw", "id": "ebdbf2bd-4a46-4757-b90e-348f9eadfd6f", "order_status_code": "PP0000", "error_status_code": "FBP100", "invoice_status_code": "*", "event": "cancel", "source_system": "cora_user", "target_event_handler": "cancel-nco"}, {"created_at": "2025-05-22T12:50:50.138Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:50:50.138Z", "modified_by": "ffyerqw", "id": "207fbbac-bbc1-40dd-84c3-6c7c99dadc95", "order_status_code": "PP1300", "error_status_code": "FBP910", "invoice_status_code": "*", "event": "cancel", "source_system": "cora_user", "target_event_handler": "cancel-nco"}, {"created_at": "2025-05-22T12:50:50.138Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:50:50.138Z", "modified_by": "ffyerqw", "id": "d3a54239-11f3-4a4d-98fd-88f8a21d3d7b", "order_status_code": "PP1300", "error_status_code": "FBP500", "invoice_status_code": "*", "event": "cancel", "source_system": "cora_user", "target_event_handler": "cancel-nco"}, {"created_at": "2025-05-22T12:50:50.138Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:50:50.138Z", "modified_by": "ffyerqw", "id": "042d8c98-c772-4e29-9cfb-612bf46163ee", "order_status_code": "PP1300", "error_status_code": "FBP920", "invoice_status_code": "*", "event": "cancel", "source_system": "cora_user", "target_event_handler": "cancel-nco"}, {"created_at": "2025-05-22T12:50:50.138Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:50:50.138Z", "modified_by": "ffyerqw", "id": "b52cdfc7-df87-43fc-87e8-6d7931bcc6c9", "order_status_code": "PP1300", "error_status_code": "LBP200", "invoice_status_code": "*", "event": "cancel", "source_system": "cora_user", "target_event_handler": "cancel-nco"}, {"created_at": "2025-05-22T12:50:50.138Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:50:50.138Z", "modified_by": "ffyerqw", "id": "a0844641-007f-4d75-9297-69257eda37bc", "order_status_code": "PP2000", "error_status_code": "LBP200", "invoice_status_code": "*", "event": "cancel", "source_system": "cora_user", "target_event_handler": "cancel-nco"}, {"created_at": "2025-05-22T12:50:50.138Z", "created_by": "ffyerqw", "modified_at": "2025-05-22T12:50:50.138Z", "modified_by": "ffyerqw", "id": "8efd98a3-ce0a-4ad2-8598-2c73dadb7468", "order_status_code": "PP2200", "error_status_code": "null", "invoice_status_code": "*", "event": "cancel", "source_system": "cora_user", "target_event_handler": "cancel-nco"}, {"created_at": "2025-05-23T09:20:53.552Z", "created_by": "ffyerqw", "modified_at": "2025-05-23T09:20:53.552Z", "modified_by": "ffyerqw", "id": "2c911967-e850-43a3-b118-ded3a3bdad28", "order_status_code": "ID0000", "error_status_code": "*", "invoice_status_code": "*", "event": "move-to-inventory", "source_system": "cora_user", "target_event_handler": "move-nco-to-inventory"}, {"created_at": "2025-05-23T09:20:53.552Z", "created_by": "ffyerqw", "modified_at": "2025-05-23T09:20:53.552Z", "modified_by": "ffyerqw", "id": "87ecc31f-232e-424b-815b-8187dffdbb51", "order_status_code": "ID5000", "error_status_code": "*", "invoice_status_code": "*", "event": "remove-from-inventory", "source_system": "cora_user", "target_event_handler": "remove-nco-from-inventory"}, {"created_at": "2025-05-26T13:39:44.552Z", "created_by": "ffyerqw", "modified_at": "2025-05-26T13:39:44.552Z", "modified_by": "ffyerqw", "id": "6c55a3f1-d7e7-40f0-bcc9-9fe91304f32e", "order_status_code": "PP2000", "error_status_code": "null", "invoice_status_code": "*", "event": "cancel", "source_system": "cora_user", "target_event_handler": "cancel-nco"}, {"created_at": "2025-06-03T11:27:04.613Z", "created_by": "ffyerqw", "modified_at": "2025-06-03T11:27:04.613Z", "modified_by": "ffyerqw", "id": "15bf0818-e54b-4c91-881a-7cc388d89e55", "order_status_code": "PP*", "error_status_code": "*", "invoice_status_code": "*", "event": "pvms-update-data", "source_system": "pvms", "target_event_handler": "pvms-update-nco"}, {"created_at": "2025-06-03T11:27:04.613Z", "created_by": "ffyerqw", "modified_at": "2025-06-03T11:27:04.613Z", "modified_by": "ffyerqw", "id": "0d5a65dd-7c74-46bf-976f-5a64df38611b", "order_status_code": "IP*", "error_status_code": "*", "invoice_status_code": "*", "event": "pvms-update-data", "source_system": "pvms", "target_event_handler": "pvms-update-nco"}, {"created_at": "2025-06-03T11:27:04.613Z", "created_by": "ffyerqw", "modified_at": "2025-06-03T11:27:04.613Z", "modified_by": "ffyerqw", "id": "51c958b2-d0c3-4b68-a097-11922c3a119d", "order_status_code": "ID*", "error_status_code": "*", "invoice_status_code": "*", "event": "pvms-update-data", "source_system": "pvms", "target_event_handler": "pvms-update-nco"}, {"created_at": "2025-06-03T11:27:04.613Z", "created_by": "ffyerqw", "modified_at": "2025-06-03T11:27:04.613Z", "modified_by": "ffyerqw", "id": "fdc48721-3da4-4fd6-9358-06e6a1b2e9c7", "order_status_code": "OC*", "error_status_code": "*", "invoice_status_code": "*", "event": "pvms-update-data", "source_system": "pvms", "target_event_handler": "pvms-update-nco"}, {"created_at": "2025-07-09T15:11:16.056Z", "created_by": "p358886", "modified_at": "2025-07-09T15:11:16.056Z", "modified_by": "p358886", "id": "90f50413-001f-4ce8-9508-8222c977d851", "order_status_code": "PP*", "error_status_code": "*", "invoice_status_code": "*", "event": "p06-update-data", "source_system": "p06", "target_event_handler": "p06-update-nco"}, {"created_at": "2025-07-09T15:11:16.056Z", "created_by": "p358886", "modified_at": "2025-07-09T15:11:16.056Z", "modified_by": "p358886", "id": "ab3c037b-6fca-427d-ae97-a42b62490086", "order_status_code": "IP*", "error_status_code": "*", "invoice_status_code": "*", "event": "p06-update-data", "source_system": "p06", "target_event_handler": "p06-update-nco"}, {"created_at": "2025-07-09T15:11:16.056Z", "created_by": "p358886", "modified_at": "2025-07-09T15:11:16.056Z", "modified_by": "p358886", "id": "112ee2be-abfa-429e-b836-d255025148e6", "order_status_code": "ID*", "error_status_code": "*", "invoice_status_code": "*", "event": "p06-update-data", "source_system": "p06", "target_event_handler": "p06-update-nco"}, {"created_at": "2025-07-09T15:11:16.056Z", "created_by": "p358886", "modified_at": "2025-07-09T15:11:16.056Z", "modified_by": "p358886", "id": "696889f1-f908-4092-9f31-c72c735a5973", "order_status_code": "OC*", "error_status_code": "*", "invoice_status_code": "*", "event": "p06-update-data", "source_system": "p06", "target_event_handler": "p06-update-nco"}, {"created_at": "2025-07-18T12:53:13.513Z", "created_by": "p358886", "modified_at": "2025-07-18T12:53:13.513Z", "modified_by": "p358886", "id": "b828b48a-5488-410f-a657-e359dc6069be", "order_status_code": "*", "error_status_code": "*", "invoice_status_code": "*", "event": "update-ordertype-from-css", "source_system": "css", "target_event_handler": "update-nco-from-css"}]}