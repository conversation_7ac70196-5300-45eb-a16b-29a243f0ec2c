{"exportedAt": "2025-07-25T13:33:33.451Z", "source": "INT", "tableName": "outbound_process_rules", "recordCount": 14, "records": [{"created_at": "2025-04-14T09:28:20.531Z", "created_by": "ffyerqw", "modified_at": "2025-05-21T14:08:41.734Z", "modified_by": "ffyerqw", "id": "1f06d847-0962-4d58-956b-9d26b39dd2f5", "source_event_handler": "update-nco-core", "event_result": "success", "order_status_code": "*", "error_status_code": "*", "invoice_status_code": "*"}, {"created_at": "2025-04-29T08:49:49.243Z", "created_by": "ffyerqw", "modified_at": "2025-05-26T13:45:03.870Z", "modified_by": "ffyerqw", "id": "a69e8aeb-2023-48fc-8bc8-778eca7e5199", "source_event_handler": "cancel-nco", "event_result": "success", "order_status_code": "OX0000", "error_status_code": "null", "invoice_status_code": "*"}, {"created_at": "2025-05-19T11:30:13.093Z", "created_by": "ffyerqw", "modified_at": "2025-05-19T11:30:13.093Z", "modified_by": "ffyerqw", "id": "a529c587-b2bd-4c4a-8478-2035e5e471cb", "source_event_handler": "deallocate-quota", "event_result": "success", "order_status_code": "PP1300", "error_status_code": "FBP500", "invoice_status_code": "*"}, {"created_at": "2025-05-19T11:30:13.093Z", "created_by": "ffyerqw", "modified_at": "2025-05-19T11:30:13.093Z", "modified_by": "ffyerqw", "id": "8bdb707c-6d92-45c7-adea-1150fe326185", "source_event_handler": "cancel-nco-scheduled", "event_result": "success", "order_status_code": "OX0000", "error_status_code": "null", "invoice_status_code": "*"}, {"created_at": "2025-05-20T16:08:39.539Z", "created_by": "p358886", "modified_at": "2025-05-21T14:08:41.734Z", "modified_by": "ffyerqw", "id": "5cffef91-8dc7-4abc-9eb0-708c24e5ac25", "source_event_handler": "update-nco", "event_result": "success", "order_status_code": "*", "error_status_code": "*", "invoice_status_code": "*"}, {"created_at": "2025-05-21T11:09:58.907Z", "created_by": "ffyerqw", "modified_at": "2025-05-21T11:09:58.907Z", "modified_by": "ffyerqw", "id": "3ecc40d5-3113-44e5-aa00-a8809ad0a7fa", "source_event_handler": "convert-pi", "event_result": "success", "order_status_code": "PP0000", "error_status_code": "null", "invoice_status_code": "PI0000"}, {"created_at": "2025-05-21T14:08:38.225Z", "created_by": "ffyerqw", "modified_at": "2025-05-21T14:08:38.225Z", "modified_by": "ffyerqw", "id": "ece8e2a0-4bf3-44f8-9d02-78db6f2fc86c", "source_event_handler": "create-nco", "event_result": "success", "order_status_code": "PP0000", "error_status_code": "null", "invoice_status_code": "PI0000"}, {"created_at": "2025-05-21T14:08:38.225Z", "created_by": "ffyerqw", "modified_at": "2025-05-21T14:08:38.225Z", "modified_by": "ffyerqw", "id": "55c4d3d0-2112-4a75-863a-2f5560808db7", "source_event_handler": "total-loss-report", "event_result": "success", "order_status_code": "OX1100", "error_status_code": "null", "invoice_status_code": "null"}, {"created_at": "2025-05-21T14:08:38.225Z", "created_by": "ffyerqw", "modified_at": "2025-05-21T14:08:38.225Z", "modified_by": "ffyerqw", "id": "3ea6bd04-8086-4edb-b4a3-2b3d890e0b11", "source_event_handler": "total-loss-persist", "event_result": "success", "order_status_code": "OX1000", "error_status_code": "null", "invoice_status_code": "*"}, {"created_at": "2025-05-21T14:08:38.225Z", "created_by": "ffyerqw", "modified_at": "2025-05-21T14:08:38.225Z", "modified_by": "ffyerqw", "id": "3c0b9039-e32a-40b9-a465-310a728c6258", "source_event_handler": "total-loss-revoke", "event_result": "success", "order_status_code": "CUSTOM", "error_status_code": "CUSTOM", "invoice_status_code": "CUSTOM"}, {"created_at": "2025-05-23T09:22:07.034Z", "created_by": "ffyerqw", "modified_at": "2025-05-23T09:22:07.034Z", "modified_by": "ffyerqw", "id": "efd46d28-275d-4ff8-a6b3-cab7e26a0f7e", "source_event_handler": "move-nco-to-inventory", "event_result": "success", "order_status_code": "ID5000", "error_status_code": "null", "invoice_status_code": "*"}, {"created_at": "2025-05-23T09:22:07.034Z", "created_by": "ffyerqw", "modified_at": "2025-05-23T09:22:07.034Z", "modified_by": "ffyerqw", "id": "0d271b18-7101-4440-9802-82ac910a3487", "source_event_handler": "remove-nco-from-inventory", "event_result": "success", "order_status_code": "ID0000", "error_status_code": "null", "invoice_status_code": "*"}, {"created_at": "2025-06-03T11:29:40.305Z", "created_by": "ffyerqw", "modified_at": "2025-06-03T11:29:40.305Z", "modified_by": "ffyerqw", "id": "63429a83-180c-4ce7-b653-28f363a2b3ff", "source_event_handler": "pvms-update-nco", "event_result": "success", "order_status_code": "CUSTOM", "error_status_code": "CUSTOM", "invoice_status_code": "CUSTOM"}, {"created_at": "2025-07-09T15:12:04.316Z", "created_by": "p358886", "modified_at": "2025-07-09T15:12:04.316Z", "modified_by": "p358886", "id": "0fd16e7f-a229-4670-8e5e-64559e2ed530", "source_event_handler": "p06-update-nco", "event_result": "success", "order_status_code": "CUSTOM", "error_status_code": "CUSTOM", "invoice_status_code": "CUSTOM"}]}