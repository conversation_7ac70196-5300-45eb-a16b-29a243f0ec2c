import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  KasD<PERSON><PERSON>d<PERSON><PERSON><PERSON>,
  Kas<PERSON><PERSON><PERSON><PERSON>,
  Kas<PERSON>odejs<PERSON>un<PERSON>,
  Kas<PERSON><PERSON><PERSON>,
  KasSqsQueue,
} from '@kas-resources/constructs';
import { aws_ssm, Duration, aws_ec2 as ec2, aws_iam as iam, RemovalPolicy, Stack } from 'aws-cdk-lib';
import { LayerVersion } from 'aws-cdk-lib/aws-lambda';
import { AuthenticationMethod, SqsEventSource } from 'aws-cdk-lib/aws-lambda-event-sources';
import { Construct } from 'constructs';
import {
  ProcessSteeringEventHandlerConstruct,
  ProcessSteeringEventHandlerConstructProps,
} from '../constructs/process-steering-event-handler-construct';
import { CoraStackPropsWithVpc } from '../types_cdk/cdk-types';
import { Constants, OneVmsEventHandlerKey } from '../utils/constants';
import { ConstantsCdk, LambdaTypeOrmBundlingExternalModules } from '../utils/constants_cdk';
import { GlobalStack } from './global-stack';

export class ProcessSteeringStack extends Stack {
  public constructor(scope: Construct, id: string, props: CoraStackPropsWithVpc) {
    super(scope, id, props);

    const vpc = props.vpc;

    //IMPORT ALL REQUIRED RESOURCES
    const logSubscriptionLambda = GlobalStack.getLogSubscriptionLambda(this);
    //import global loggroup kms
    const logGroupKmsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.logGroupKmsKeyArnPName,
    );
    const logGroupKey = KasKmsKey.fromKeyArn(this, 'logGroupKey', logGroupKmsKeyArn);

    // Import global Secret Key and Secrets
    const globalSecretKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalSecretKmsKeyArnPName,
    );
    const globalSecretKey = KasKmsKey.fromKeyArn(this, 'globalSecretKey', globalSecretKeyArn);

    //Import Kafka secret
    const kafkaSecretArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.kafkaSecretArnPName,
    );
    const kafkaSecret = KasSecret.fromSecretAttributes(this, id + 'CoraKafkaSecret', {
      secretCompleteArn: kafkaSecretArn,
      encryptionKey: globalSecretKey,
    });

    // Import aurora secrets
    const auroraWriterSecretArn: string = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.coraAuroraWriterSecretArnPName,
    );
    const auroraWriterSecret = KasSecret.fromSecretAttributes(this, id + 'auroraWriterSecret', {
      encryptionKey: globalSecretKey,
      secretCompleteArn: auroraWriterSecretArn,
    });

    const auroraReaderSecretArn: string = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.coraAuroraReaderSecretArnPName,
    );
    const auroraReaderSecret = KasSecret.fromSecretAttributes(this, id + 'auroraReaderSecret', {
      encryptionKey: globalSecretKey,
      secretCompleteArn: auroraReaderSecretArn,
    });

    // Retrieve all necessary security groups
    const kafkaSecurityGroupId = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.kafkaSecurityGroupPName,
    );
    const kafkaSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'KafkaAccessSecurityGroup',
      kafkaSecurityGroupId,
    );

    const auroraAccessSecurityGroupID = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.auroraAccessSecurityGroupPName,
    );
    const auroraAccessSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'AuroraAccessSecurityGroup',
      auroraAccessSecurityGroupID,
    );

    const externalAccessSecurityGroupId = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.externalAccessSecurityGroupPName,
    );
    const externalAccessSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'ExternalAccessSecurityGroup',
      externalAccessSecurityGroupId,
    );

    // Import global sqs kms key key
    const globalSqsKmsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalSqsKmsKeyArnPName,
    );
    const globalSqsKmsKey = KasKmsKey.fromKeyArn(this, 'globalSqsKmsKey', globalSqsKmsKeyArn);

    //Create Event Bridge Scheduler resources
    const globalEventbridgeSchedulerKmsKeyId = Constants.buildKmsKeyId(
      props.stage,
      'global-eventbridge-scheduler-kms-key',
    );
    const globalEventbridgeSchedulerKmsKey = new KasKmsKey(this, globalEventbridgeSchedulerKmsKeyId);
    globalEventbridgeSchedulerKmsKey.grantEncryptDecrypt(new iam.ServicePrincipal('scheduler.amazonaws.com'));

    const eventBridgeSchedulerRole = new iam.Role(this, 'CoraEventBridgeSchedulerRole', {
      roleName: 'CoraEventBridgeSchedulerRole',
      assumedBy: new iam.ServicePrincipal('scheduler.amazonaws.com'),
    });
    globalEventbridgeSchedulerKmsKey.grantDecrypt(eventBridgeSchedulerRole);

    //import quota api secret
    const _quotaApiSecretArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.quotaApiSecretArnPName,
    );
    const quotaApiSecret = KasSecret.fromSecretAttributes(this, id + 'CoraQuotaApiSecretInterface', {
      encryptionKey: globalSecretKey,
      secretCompleteArn: _quotaApiSecretArn,
    });

    //TypeORM Lambda Layer
    const typeORMLayerArn = aws_ssm.StringParameter.fromStringParameterAttributes(this, id + 'LoadTypeORMLayerArn', {
      parameterName: props.globalParameterNames.typeORMLambdaLayerArnPName,
    }).stringValue;
    const typeORMLayer = LayerVersion.fromLayerVersionArn(this, id + 'TypeORMLayerInterface', typeORMLayerArn);

    //import global dynamodb table key
    const coraGlobalDynamoKmsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalDynamoKmsKeyArnPName,
    );
    const coraGlobalDynamoKmsKey = KasKmsKey.fromKeyArn(this, 'globalDynamoKmsKey', coraGlobalDynamoKmsKeyArn);

    //import cora org rel table
    const coraOrgRelDdbName = Constants.buildResourceName(
      props.stage,
      Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName,
    );
    const orgRelTable = KasDynamodbTable.fromTableAttributes(this, 'CoraOrgRelTable', {
      tableName: coraOrgRelDdbName,
      encryptionKey: coraGlobalDynamoKmsKey,
    });

    //import global cora-md dynamodb kms key
    const coraGlobalDynamoMdKmsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalDynamoMdKmsKeyArnPName,
    );
    const mdGlobalDynamoTableKey = KasKmsKey.fromKeyArn(this, 'globalDynamoMdKmsKey', coraGlobalDynamoMdKmsKeyArn);

    //import all masterdata dynamodb tables with global key
    const impTableDdbName = `${Constants.MASTER_DATA_APP_NAME}-${props.stage}-${Constants.DYNAMODB_BOSS_IMPORTER_TABLE_NAME}`;
    const impTable = KasDynamodbTable.fromTableAttributes(this, 'ImpTable', {
      tableName: impTableDdbName,
      encryptionKey: mdGlobalDynamoTableKey,
    });

    const dlrTableDdbName = `${Constants.MASTER_DATA_APP_NAME}-${props.stage}-${Constants.DYNAMODB_BOSS_DEALER_TABLE_NAME}`;
    const dlrTable = KasDynamodbTable.fromTableAttributes(this, 'DlrTable', {
      tableName: dlrTableDdbName,
      encryptionKey: mdGlobalDynamoTableKey,
    });

    const scTableDdbName = `${Constants.MASTER_DATA_APP_NAME}-${props.stage}-${Constants.DYNAMODB_CORA_SHIPPING_CODE_TABLE_NAME}`;
    const scTable = KasDynamodbTable.fromTableAttributes(this, 'ScTable', {
      tableName: scTableDdbName,
      encryptionKey: mdGlobalDynamoTableKey,
    });

    const otTableDdbName = `${Constants.MASTER_DATA_APP_NAME}-${props.stage}-${Constants.DYNAMODB_CORA_ORDER_TYPE_TABLE_NAME}`;
    const otTable = KasDynamodbTable.fromTableAttributes(this, 'OtTable', {
      tableName: otTableDdbName,
      encryptionKey: mdGlobalDynamoTableKey,
    });

    //Event dispatcher inbound DLQ
    const eventDispatcherInboundDlq = new KasSqsQueue(this, 'EventDispatcherInboundDlq', {
      queueName: 'EventDispatcherInboundDlq',
      encryptionMasterKey: globalSqsKmsKey,
    });

    //Event dispatcher inbound queue
    const eventDispatcherInboundQueue = new KasSqsQueue(this, 'EventDispatcherInboundQueue', {
      queueName: 'EventDispatcherInboundQueue',
      encryptionMasterKey: globalSqsKmsKey,
      visibilityTimeout: Duration.minutes(5), //must be >= lambda timeout
      deadLetterQueue: { queue: eventDispatcherInboundDlq, maxReceiveCount: 1 },
    });
    //Grant access to event dispatcher inbound queue to event bridge scheduler role
    eventDispatcherInboundQueue.grantSendMessages(eventBridgeSchedulerRole);

    //EVENT HANDLER SHARED RESOURCES
    //Global Event handler inbound DLQ
    const globalEventHandlerInboundDlq = new KasSqsQueue(this, 'EventHandlerInboundDlq', {
      queueName: 'EventHandlerInboundDlq',
      encryptionMasterKey: globalSqsKmsKey,
    });

    const commonEventHandlerProps = {
      eventDispatcherInboundQueue,
      globalEventHandlerInboundDlq,
      kafkaSecret,
      kafkaBrokers: props.kafkaParameters.brokers,
      notificationTopic: props.kafkaParameters.ncoNotificationTopic,
      logGroupKey,
      orgRelTable,
      scTable,
      otTable,
      dlrTable,
      impTable,
      logSubscriptionLambda,
      vpc,
      auroraAccessSecurityGroup,
      kafkaSecurityGroup,
      auroraWriterSecret,
      globalSqsKmsKey,
      typeORMLayer,
      stage: props.stage,
      applicationNameToAuthorize: props.applicationNameToAuthorize,
    };

    //DEFINITION OF EVENT HANDLERS, AUTOGENERATED FROM OneVmsEventHandlerKey ENUM
    const eventHandlerKeys: OneVmsEventHandlerKey[] = Object.values(OneVmsEventHandlerKey);
    const eventHandlerConstructs: ProcessSteeringEventHandlerConstruct[] = [];

    const eventHandlerKeysWithScheduler: OneVmsEventHandlerKey[] = [
      OneVmsEventHandlerKey.DEALLOCATE_QUOTA,
      OneVmsEventHandlerKey.TOTAL_LOSS_REPORT,
    ];

    //Add event handler keys that use quota api
    const eventHandlerKeysWithQuotaApi: OneVmsEventHandlerKey[] = [
      OneVmsEventHandlerKey.CONVERT_PI,
      OneVmsEventHandlerKey.CREATE_NCO,
      OneVmsEventHandlerKey.UPDATE_NCO,
    ];

    //Add event handler keys that should be excluded from construct creation
    const eventHandlerKeysToExclude: OneVmsEventHandlerKey[] = [OneVmsEventHandlerKey.INTEGRATION_TEST];
    if (!props.featureFlags.p06DataSync) {
      eventHandlerKeysToExclude.push(OneVmsEventHandlerKey.P06_UPDATE_NCO);
    }

    //Create event handler resources for every key in the array
    for (const eventHandlerKey of eventHandlerKeys) {
      if (eventHandlerKeysToExclude.includes(eventHandlerKey)) {
        continue;
      }
      const eventHandlerProps: ProcessSteeringEventHandlerConstructProps = {
        eventHandlerKey: eventHandlerKey,
        ...commonEventHandlerProps,
      };

      //add params for Eventbridge Scheduler if needed
      if (eventHandlerKeysWithScheduler.includes(eventHandlerKey)) {
        //Add event bridge scheduler role and kms key to the event handler props
        eventHandlerProps.eventBridgeScheduleProps = {
          eventBridgeSchedulerRole,
          globalEventbridgeSchedulerKmsKey,
        };
        //This is needed because EventBridge Scheduler Service has no VPC endpoints and can only be accessed through the NAT Gateway.
        eventHandlerProps.additionalSecurityGroups = [externalAccessSecurityGroup];
      }

      //add params for quota API if needed
      if (eventHandlerKeysWithQuotaApi.includes(eventHandlerKey)) {
        eventHandlerProps.quotaApiSecret = quotaApiSecret;
        eventHandlerProps.extraEnvs = {
          QUOTA_API_SECRET_ARN: quotaApiSecret.secretArn,
          ALLOW_QUOTA_API_MOCK: props.stage === 'dev' ? 'true' : 'false',
        };
        //Add additional security group for external access to quota api and ppn auth endpoint
        eventHandlerProps.additionalSecurityGroups = [externalAccessSecurityGroup];
      }

      const eventHandlerConstruct = new ProcessSteeringEventHandlerConstruct(
        this,
        `${eventHandlerKey}EventHandlerConstruct`,
        eventHandlerProps,
      );
      // Store the created construct in the constructs array
      eventHandlerConstructs.push(eventHandlerConstruct);
    }

    //Event hander key to queue url mapping for the dispatcher envs
    const eventHandlerQueuesMap = eventHandlerConstructs.reduce(
      (acc, construct) => ({
        ...acc,
        [construct.eventHandlerKey]: construct.eventHandlerInboundQueue.queueUrl,
      }),
      {},
    ) as Record<OneVmsEventHandlerKey, string>;

    //EVENT DISPATCHER RESOURCES
    //Event dispatcher lambda
    const eventDispatcherLambda = new KasNodejsFunction(this, 'EventDispatcherLambda', {
      vpc: props.vpc,
      securityGroups: [kafkaSecurityGroup, auroraAccessSecurityGroup],
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-event-dispatcher`,
      entry: `lambda/backend/process-steering/event-dispatcher/index.ts`,
      handler: 'index.handler',
      timeout: Duration.minutes(5),
      memorySize: 522,
      runtime: ConstantsCdk.NODE_JS_VERSION,
      customManagedKey: logGroupKey,
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      errHandlingLambda: logSubscriptionLambda,
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
      environment: {
        KAFKA_SECRET_ARN: kafkaSecret.secretArn,
        KAFKA_BROKERS: JSON.stringify(props.kafkaParameters.brokers),
        KAFKA_TOPIC_NOTIFICATION: props.kafkaParameters.ncoNotificationTopic,
        AURORA_SECRET_ARN: auroraReaderSecret.secretArn,
        EVENT_HANDLER_QUEUES: JSON.stringify(eventHandlerQueuesMap),
      },
      layers: [typeORMLayer],
      stage: props.stage,
    });

    //Add required access to tables, secrets, etc
    kafkaSecret.grantRead(eventDispatcherLambda);
    auroraReaderSecret.grantRead(eventDispatcherLambda);
    eventDispatcherInboundQueue.grantConsumeMessages(eventDispatcherLambda);

    //Add send rights on all event handler inbound queues
    eventHandlerConstructs.forEach((construct) => {
      construct.eventHandlerInboundQueue.grantSendMessages(eventDispatcherLambda);
    });

    //Event source to consume sqs messages from eventDispatcherInboundQueue by eventDispatcherLambda
    const eventDispatcherEventSource = new SqsEventSource(eventDispatcherInboundQueue, {
      enabled: true,
      batchSize: 1, //TODO what should be set here?
      reportBatchItemFailures: true,
    });
    eventDispatcherLambda.addEventSource(eventDispatcherEventSource);

    //SSM Parameter for event dispatcher inbound queue
    new aws_ssm.StringParameter(this, 'DispatcherInboundQueueArnParameter', {
      parameterName: props.globalParameterNames.eventDispatcherInboundQueueArnPName,
      stringValue: eventDispatcherInboundQueue.queueArn,
    });

    //import Transaction table
    const coraTransactionTableDdbName = Constants.buildResourceName(
      props.stage,
      Constants.DYNAMODB_TRANSACTION_TABLE_PARAMS.tableName,
    );
    const transactionTable = KasDynamodbTable.fromTableAttributes(this, 'transactionTable', {
      tableName: coraTransactionTableDdbName,
      encryptionKey: coraGlobalDynamoKmsKey,
    });

    //import SubTransaction table
    const coraSubtransactionTableDdbName = Constants.buildResourceName(
      props.stage,
      Constants.DYNAMODB_SUBTRANSACTION_TABLE_PARAMS.tableName,
    );
    const subransactionTable = KasDynamodbTable.fromTableAttributes(this, 'subtransactionTable', {
      tableName: coraSubtransactionTableDdbName,
      encryptionKey: coraGlobalDynamoKmsKey,
    });

    // Notification ingest lambda
    const notificationIngestLambdaName = `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-notification-ingest-consumer`;
    const groupId = `FRA_one_vms_cora_notifications_new_car_order_${props.stage}`;
    const notificationIngestLambda = new KafkaConsumerLambda(this, notificationIngestLambdaName, {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      description: 'Cora Lambda to ingest new car order transaction notifications',
      customManagedKey: logGroupKey,
      entry: 'lambda/backend/notification-center/notification-ingest/nco-event/index.ts',
      functionName: notificationIngestLambdaName,
      kafkaBrokers: props.kafkaParameters.brokers,
      kafkaBatchSize: 500,
      kafkaImportGroup: groupId,
      kafkaImportTopic: props.kafkaParameters.ncoNotificationTopic,
      kafkaSecret: kafkaSecret,
      extraEnvVars: {
        NOTIFICATION_KAFKA_TOPIC: props.kafkaParameters.ncoNotificationTopic,
        TRANSACTIONS_TABLE_NAME: transactionTable.tableName,
        SUBTRANSACTIONS_TABLE_NAME: subransactionTable.tableName,
      },
      eventSourceAuthenticationMethod: AuthenticationMethod.BASIC_AUTH,
      kafkaSecretKmsKeyAlias: Constants.buildKmsKeyId(props.stage, Constants.KMS_KEY_GLOBAL_SECRET_NAME),
      errHandlingLambda: logSubscriptionLambda,
      vpc: vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [kafkaSecurityGroup],
      stage: props.stage,
    });

    transactionTable.grantReadWriteData(notificationIngestLambda);
    subransactionTable.grantReadWriteData(notificationIngestLambda);
  }
}
