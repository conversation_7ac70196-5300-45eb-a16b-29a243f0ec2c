import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ka<PERSON><PERSON><PERSON><PERSON>, KasSns<PERSON><PERSON>ic, KasSqsQueue } from '@kas-resources/constructs';
import { AuthenticationMethod } from 'aws-cdk-lib/aws-lambda-event-sources';
import { Construct } from 'constructs';
import { OneVmsEventKey } from '../types/process-steering-types';
import { CoraStackPropsWithVpc } from '../types_cdk/cdk-types';
import { Constants } from '../utils/constants';
import { ConstantsCdk, LambdaTypeOrmBundlingExternalModules } from '../utils/constants_cdk';
import { Stack, Duration, aws_ssm as ssm, RemovalPolicy, aws_ec2 as ec2 } from 'aws-cdk-lib';
import { LayerVersion } from 'aws-cdk-lib/aws-lambda';
import { StringParameter } from 'aws-cdk-lib/aws-ssm';
import { GlobalStack } from './global-stack';
import { SnsDestination } from 'aws-cdk-lib/aws-lambda-destinations';

export class P06OrderDataSyncStack extends Stack {
  public constructor(scope: Construct, id: string, props: CoraStackPropsWithVpc) {
    super(scope, id, props);

    const vpc = props.vpc;

    const logSubscriptionLambda = GlobalStack.getLogSubscriptionLambda(this);
    //import global loggroup kms
    const logGroupKmsKeyArn = ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.logGroupKmsKeyArnPName,
    );
    const logGroupKey = KasKmsKey.fromKeyArn(this, 'logGroupKey', logGroupKmsKeyArn);

    // Import global Secret Key and Secrets
    const globalSecretKeyArn = ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalSecretKmsKeyArnPName,
    );
    const globalSecretKey = KasKmsKey.fromKeyArn(this, 'globalSecretKey', globalSecretKeyArn);

    const auroraReaderSecretArn: string = ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.coraAuroraReaderSecretArnPName,
    );
    const auroraReaderSecret = KasSecret.fromSecretAttributes(this, id + 'auroraReaderSecret', {
      encryptionKey: globalSecretKey,
      secretCompleteArn: auroraReaderSecretArn,
    });

    // Kafka Parameters
    const _kafkaSecretArn = ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.kafkaSecretArnPName,
    );

    const kafkaSecret = KasSecret.fromSecretAttributes(this, id + 'CoraKafkaSecretInterface', {
      encryptionKey: globalSecretKey,
      secretCompleteArn: _kafkaSecretArn,
    });

    // Import global health monitoring topic and key
    const healthMonitoringTopicKeyArn = ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.healthMonitoringTopicKmsKeyArnPName,
    );
    const healthMonitoringTopicKey = KasKmsKey.fromKeyArn(
      this,
      'healthMonitoringTopicKey',
      healthMonitoringTopicKeyArn,
    );

    const _healthMonitoringTopicArn = ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.healthMonitoringTopicArnPName,
    );
    const healthMonitoringTopic = KasSnsTopic.fromTopicArn(
      this,
      id + 'CoraHealthMonitoringTopicInterface',
      _healthMonitoringTopicArn,
    );

    // Import global sqs kms key key
    const globalSqsKmsKeyArn = ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalSqsKmsKeyArnPName,
    );
    const globalSqsKmsKey = KasKmsKey.fromKeyArn(this, 'globalSqsKmsKey', globalSqsKmsKeyArn);

    const eventDispatcherInboundQueueArn = ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.eventDispatcherInboundQueueArnPName,
    );
    const eventDispatcherInboundQueue = KasSqsQueue.fromQueueAttributes(this, 'EventDispatcherInboundQueue', {
      queueArn: eventDispatcherInboundQueueArn,
      keyArn: globalSqsKmsKey.keyArn,
    });

    // Retrieve all necessary security groups
    const auroraAccessSecurityGroupID = ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.auroraAccessSecurityGroupPName,
    );
    const auroraAccessSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'AuroraAccessSecurityGroup',
      auroraAccessSecurityGroupID,
    );

    const kafkaSecurityGroupId = ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.kafkaSecurityGroupPName,
    );
    const kafkaSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'KafkaAccessSecurityGroup',
      kafkaSecurityGroupId,
    );

    //TypeORM Lambda Layer
    const typeORMLayerArn = StringParameter.fromStringParameterAttributes(this, id + 'LoadTypeORMLayerArn', {
      parameterName: props.globalParameterNames.typeORMLambdaLayerArnPName,
    }).stringValue;
    const typeormLayer = LayerVersion.fromLayerVersionArn(this, id + 'TypeORMLayerInterface', typeORMLayerArn);

    const importP06OrderDataLambda = new KafkaConsumerLambda(this, `EventImport-${OneVmsEventKey.P06_IMPORT}`, {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-event-import-${OneVmsEventKey.P06_IMPORT}`,
      entry: 'lambda/backend/process-steering/event-import/p06/index.ts',
      handler: 'handler',
      description:
        'Consumes P06 inbound events, parses, validates and pushes them to the dispatcher for further handling.',
      environment: {
        KAFKA_SECRET_ARN: kafkaSecret.secretArn,
        KAFKA_BROKERS: JSON.stringify(props.kafkaParameters.brokers),
        AURORA_SECRET_ARN: auroraReaderSecret.secretArn,
        KAFKA_TOPIC_NOTIFICATION: props.kafkaParameters.ncoNotificationTopic,
        DISPATCHER_QUEUE_URL: eventDispatcherInboundQueue.queueUrl,
      },
      timeout: Duration.minutes(1),
      memorySize: 256,
      customManagedKey: logGroupKey,
      kafkaSecret: kafkaSecret,
      kafkaBrokers: props.kafkaParameters.brokers,
      kafkaImportGroup: `FRA_one_vms_cora_p06_sync_consumer_group_${props.stage}_003`,
      kafkaImportTopic: props.kafkaParameters.p06InboundTopic,
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      eventSourceAuthenticationMethod: AuthenticationMethod.BASIC_AUTH,
      //send kafka consumption errors directly to health monitoring topic
      onFailureDestination: new SnsDestination(healthMonitoringTopic),
      errHandlingLambda: logSubscriptionLambda,
      vpc: vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [auroraAccessSecurityGroup, kafkaSecurityGroup],
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
      layers: [typeormLayer],
      stage: props.stage,
    });

    eventDispatcherInboundQueue.grantSendMessages(importP06OrderDataLambda);
    kafkaSecret.grantRead(importP06OrderDataLambda);
    auroraReaderSecret.grantRead(importP06OrderDataLambda);

    healthMonitoringTopic.grantPublish(importP06OrderDataLambda);
    healthMonitoringTopicKey.grantEncryptDecrypt(importP06OrderDataLambda);
  }
}
