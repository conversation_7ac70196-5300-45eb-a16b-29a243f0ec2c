import { Environment, aws_iam as iam } from 'aws-cdk-lib';
import { GlobalParameterNames } from '../context/global-parameter-names';
import { ISecurityGroup, IVpc } from 'aws-cdk-lib/aws-ec2';
import { KasKms<PERSON><PERSON>, KasStage } from '@kas-resources/constructs';
import { CronOptions } from 'aws-cdk-lib/aws-events';
import { IAuthorizer, IResource } from 'aws-cdk-lib/aws-apigateway';
import { IFunction } from 'aws-cdk-lib/aws-lambda';

export interface CoraStackPropsWithVpc extends CoraStackProps {
  vpc: IVpc;
}

export interface CoraFeatureFlags {
  copyOrder: boolean;
  updateNcoCoreData: boolean;
  reportRevokeTotalLoss: boolean;
  deallocateQuota: boolean;
  importerTransfer: boolean;
  handleDealerInventory: boolean;
  buySellTransfer: boolean;
  ncoInvoiceMapping: boolean;
  p06DataSync: boolean;
}

export interface CoraStackProps {
  stage: KasStage;
  hostedZoneName: string;
  paddockDomainName: string;
  mail: string;
  owner: string;
  useCase: string;
  env: Environment;
  idp: {
    domain: string;
    loginUrl: string;
    tokenUrl: string;
    clientId: string;
    publicKeyUrl: string;
    issuer: string;
  };
  kasAuthEndpointUrl: string;
  applicationNameToAuthorize: string;
  ppnRolesWrite: string[];
  globalParameterNames: GlobalParameterNames;
  alerting: {
    alertingEmails: string[];
  };
  kafkaParameters: {
    brokers: string[];
    newCarOrderTopicOneVms: string;
    newCarOrderTopicPvms: string;
    newCarOrderTopicP06: string;
    newCarOrderTopicConfigRequest: string;
    newCarOrderTopicConfigResponse: string;
    hubModelTypeVisibilityTopic: string;
    bossOrgTopic: string;
    quotaTopic: string;
    pvmsOrderDataTopic: string;
    p06InboundTopic: string;
    pccdModelTypeTextTopic: string;
    ncoNotificationTopic: string;
  };
  featureFlags: CoraFeatureFlags;
  disasterRecovery?: {
    applicationShortName: string;
    targetBackupVaultArn: string;
    backupName: string;
    targetAccount: string;
    backupTags: {
      key: string;
      value: string;
    }[];
    backupCronSchedule?: CronOptions;
  };
}

export interface BackendEndpointProps {
  authorizer: IAuthorizer;
  stage: KasStage;
  corsDomain: string;
  logGroupKey: KasKmsKey;
  parentResource: IResource;
  logSubscriptionLambda: IFunction;
  vpcEndpointsSecurityGroup: ISecurityGroup;
  vpc: IVpc;
}

export interface EventBridgeScheduleProps {
  eventBridgeSchedulerRole: iam.Role;
  globalEventbridgeSchedulerKmsKey: KasKmsKey;
}
