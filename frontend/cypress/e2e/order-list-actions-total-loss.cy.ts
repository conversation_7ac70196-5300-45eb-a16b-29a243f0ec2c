import {
  NotificationStatus,
  OneVmsEventKey,
  SpecialStatusCode,
} from '../../../infrastructure/lib/types/process-steering-types';
import {
  CORA_NEW_CAR_ORDER_STATUS_REPORT_TOTAL_LOSS,
  NOTIFICATION_CENTER_TRANSACTION_BASE_URL,
  USERNAME_WRITE,
} from '../support/constants';
import { generateMtvs, generateNewCarOrders } from '../support/order-lists-test-data';
import { checkTransactionResponse } from '../support/utils';

const displayInDistOrdersTab = '[data-e2e="display_orders_in_distribution"]';
const displayInTotalLossOrdersTab = '[data-e2e="display_orders_in_total_loss"]';
const orderActionsBtn = '[data-e2e="open_actions"]';
const tippyDropdown = '[data-tippy-root]';
const reportTotalLossBtn = '[data-e2e="report_total_loss"]';
const reportTotalLossBtnMulti = '[data-e2e="report_total_loss"]';
const revokeTotalLossBtn = '[data-e2e="revoke_total_loss"]';
const revokeTotalLossBtnMulti = '[data-e2e="revoke_total_loss"]';

const reportModal = `[data-e2e="${OneVmsEventKey.TOTAL_LOSS_REPORT}_order_modal"]`;
const revokeModal = `[data-e2e="${OneVmsEventKey.TOTAL_LOSS_REVOKE}_order_modal"]`;
const multiDetailsContainer = `[data-e2e="${OneVmsEventKey.TOTAL_LOSS_REPORT}_multi_details"]`;
const submitOrderLocator: string = '[data-e2e="accept"]';
const closeBtn = '[data-e2e="close"]';

const ordersEndpointURL = '**/new-car-order**';
const ncoPrefixThisTest = 'TL';
const ncoPrefixThisTestRevoke = 'TLR';
const inDistributionOrders = generateNewCarOrders(ncoPrefixThisTest, 'ID0000', 3);
const inDistributionOrderMtvs = generateMtvs(inDistributionOrders);
const revokableOrders = generateNewCarOrders(ncoPrefixThisTestRevoke, CORA_NEW_CAR_ORDER_STATUS_REPORT_TOTAL_LOSS, 3);
const revokableOrderMtvs = generateMtvs(revokableOrders);

describe('Order List Actions Total Loss', () => {
  beforeEach(() => {
    cy.login(USERNAME_WRITE);
    cy.task('prepareMtvRds', { objs: [...inDistributionOrderMtvs, ...revokableOrderMtvs] }, { timeout: 10000 });
    cy.task('prepareNcoRds', { objs: [...inDistributionOrders, ...revokableOrders] }, { timeout: 10000 });
    cy.visit('/lists/orders');
  });

  afterEach(() => {
    cy.task('cleanupNcoRds', {
      ids: [...inDistributionOrders, ...revokableOrders].map((order) => order.pk_new_car_order_id),
    });
    cy.task('cleanupMtvRds', { objs: [...inDistributionOrderMtvs, ...revokableOrderMtvs] });
  });

  it('Report then revoke total loss from single order in list, success case', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayInDistOrdersTab).click();
    cy.get(displayInDistOrdersTab, { timeout: 15000 }).should('be.visible');
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();

    //Report loss from first order
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.wait('@fetchOrders');
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(inDistributionOrders[0].pk_new_car_order_id);

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(reportTotalLossBtn, { timeout: 3000 }).should('be.visible').click();

    //confirm and check result (use real Cora API deallocate call)
    cy.intercept('POST', `${ordersEndpointURL}/total-loss/report`).as('reportLoss');
    cy.get(reportModal).find(submitOrderLocator).click();
    cy.wait('@reportLoss')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.EXPORTED),
        );
      });

    //Close the dialog and check if table was updated correctly
    cy.get(reportModal).find(closeBtn).click();
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayInTotalLossOrdersTab).click();
    cy.get(displayInTotalLossOrdersTab, { timeout: 15000 }).should('be.visible');

    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.wait('@fetchOrders');
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(inDistributionOrders[0].pk_new_car_order_id);
    cy.get('[row-index="0"] [col-id="order_status_onevms_code"]').contains(CORA_NEW_CAR_ORDER_STATUS_REPORT_TOTAL_LOSS);

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(revokeTotalLossBtn, { timeout: 3000 }).should('be.visible').click();

    //confirm and check result (use real Cora API deallocate call)
    cy.intercept('POST', `${ordersEndpointURL}/total-loss/revoke`).as('revokeLoss');
    cy.get(revokeModal).find(submitOrderLocator).click();
    cy.wait('@revokeLoss')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.EXPORTED),
        );
      });

    //Close the dialog and check if table was updated correctly
    cy.get(revokeModal).find(closeBtn).click();
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayInDistOrdersTab).click();
    cy.get(displayInDistOrdersTab, { timeout: 15000 }).should('be.visible');

    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.wait('@fetchOrders');

    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(inDistributionOrders[0].pk_new_car_order_id);
    cy.get('[row-index="0"] [col-id="order_status_onevms_code"]').contains(
      inDistributionOrders[0].order_status_onevms_code,
    );
    if (inDistributionOrders[0].order_status_onevms_error_code !== SpecialStatusCode.NONE) {
      cy.get('[row-index="0"] [col-id="order_status_onevms_error_code"]').contains(
        inDistributionOrders[0].order_status_onevms_error_code,
      );
    } else {
      cy.get('[row-index="0"] [col-id="order_status_onevms_error_code"]').should('have.text', '');
    }

    //Check that persist schedule was created
    cy.task<boolean>('checkNcoPersistTotalLossSchedule', inDistributionOrders[0].pk_new_car_order_id);
    cy.task('cleanupNcoPersistTotalLossSchedules', [inDistributionOrders[0].pk_new_car_order_id]);
  });

  it('Report loss from single order from list, error case (mocked cora call)', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayInDistOrdersTab).click();
    cy.get(displayInDistOrdersTab, { timeout: 15000 }).should('be.visible');
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.wait('@fetchOrders');

    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(inDistributionOrders[0].pk_new_car_order_id);

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(reportTotalLossBtn, { timeout: 3000 }).should('be.visible').click();

    //confirm and check result (use real Cora API deallocate call)
    cy.intercept('POST', `${ordersEndpointURL}/total-loss/report`, (req) => {
      req.body.nco_ids_with_modified_at = [];
      req.continue();
    });

    cy.get(reportModal).find(submitOrderLocator).click();
    cy.get(reportModal)
      .find(`.header-error-${OneVmsEventKey.TOTAL_LOSS_REPORT}`, { timeout: 10000 })
      .should('be.visible');
  });

  it('Report loss from single order from list, error case order changed (racecondition, mocked request)', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayInDistOrdersTab).click();
    cy.get(displayInDistOrdersTab, { timeout: 15000 }).should('be.visible');
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.wait('@fetchOrders');

    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(inDistributionOrders[0].pk_new_car_order_id);

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(reportTotalLossBtn, { timeout: 3000 }).should('be.visible').click();

    //confirm and check result (use real Cora API deallocate call)
    cy.intercept('POST', `${ordersEndpointURL}/total-loss/report`, (req) => {
      req.body.nco_ids_with_modified_at[0].modified_at = new Date(0).toISOString();
      req.continue();
    }).as('reportLoss');

    cy.get(reportModal).find(submitOrderLocator).click();
    cy.wait('@reportLoss')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.ERROR),
        );
      });
    cy.get(reportModal).find(closeBtn).click();
  });

  it('Revoke loss from single order from list, error case (mocked cora call)', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayInTotalLossOrdersTab).click();
    cy.get(displayInTotalLossOrdersTab, { timeout: 15000 }).should('be.visible');
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();

    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(revokableOrders[0].pk_new_car_order_id);

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(revokeTotalLossBtn, { timeout: 3000 }).should('be.visible').click();

    //confirm and check result (use real Cora API deallocate call)
    cy.intercept('POST', `${ordersEndpointURL}/total-loss/revoke`, (req) => {
      req.body.nco_ids_with_modified_at = [];
      req.continue();
    });

    cy.get(revokeModal).find(submitOrderLocator).click();
    cy.get(revokeModal)
      .find(`.header-error-${OneVmsEventKey.TOTAL_LOSS_REVOKE}`, { timeout: 10000 })
      .should('be.visible');
  });

  it('Revoke loss from single order from list, error case order changed (racecondition, mocked request)', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayInTotalLossOrdersTab).click();
    cy.get(displayInTotalLossOrdersTab, { timeout: 15000 }).should('be.visible');
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();

    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(revokableOrders[0].pk_new_car_order_id);

    cy.clickOrderActions(orderActionsBtn);
    cy.get(tippyDropdown).find(revokeTotalLossBtn, { timeout: 3000 }).should('be.visible').click();

    //confirm and check result (use real Cora API deallocate call)
    cy.intercept('POST', `${ordersEndpointURL}/total-loss/revoke`, (req) => {
      req.body.nco_ids_with_modified_at[0].modified_at = new Date(0).toISOString();
      req.continue();
    }).as('revokeLoss');

    cy.get(revokeModal).find(submitOrderLocator).click();
    cy.wait('@revokeLoss')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.ERROR),
        );
      });
    cy.get(revokeModal).find(closeBtn).click();
  });

  it('Report Loss from multiple orders from list', () => {
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayInDistOrdersTab).click();
    cy.get(displayInDistOrdersTab, { timeout: 15000 }).should('be.visible');
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();

    //Report loss from first order
    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.wait('@fetchOrders');
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');

    //check table items, mark first 2 items and click multi cancel
    cy.get('[row-index="0"]', { timeout: 20000 }).should('be.visible');
    cy.get('[row-index="1"]', { timeout: 10000 }).should('be.visible');
    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(inDistributionOrders[0].pk_new_car_order_id);
    cy.get('[row-index="1"] [col-id="pk_new_car_order_id"]').contains(inDistributionOrders[1].pk_new_car_order_id);
    cy.get(reportTotalLossBtnMulti).should('not.exist');
    cy.wait(1000);

    cy.get('[row-index="0"] [col-id="ag-Grid-ControlsColumn"] .ag-selection-checkbox input').check();
    cy.get('[row-index="1"] [col-id="ag-Grid-ControlsColumn"] .ag-selection-checkbox input').check();

    cy.wait(1000);
    cy.get(reportTotalLossBtnMulti).should('exist');
    cy.get(reportTotalLossBtnMulti).click({ force: true });

    cy.get(reportModal).should('exist').and('not.be.empty');
    cy.get(reportModal).find(multiDetailsContainer).should('be.visible');
    cy.get(reportModal).find(multiDetailsContainer).find('.col-container>.field-col').should('have.length', 2);
    cy.get(reportModal)
      .find(multiDetailsContainer)
      .find('.col-container>.field-col')
      .eq(0)
      .find('p-text')
      .should('have.length', 1);
    cy.get(reportModal)
      .find(multiDetailsContainer)
      .find('.col-container>.field-col')
      .eq(1)
      .find('p-text')
      .should('have.length', 1);

    cy.intercept('POST', `${ordersEndpointURL}/total-loss/report`).as('reportLoss');
    cy.get(reportModal).find(submitOrderLocator).click();
    cy.wait('@reportLoss')
      .its('response.body')
      .then((responseBody) => {
        cy.pollApiUntilCheckFnSucceeds(
          `${NOTIFICATION_CENTER_TRANSACTION_BASE_URL}${responseBody.data.transaction_id}`,
          (res) => checkTransactionResponse(res, NotificationStatus.EXPORTED),
        );
      });
    cy.get(reportModal).find(closeBtn).click();
    cy.intercept('POST', `${ordersEndpointURL}/get`).as('fetchOrders');
    cy.get(displayInTotalLossOrdersTab).click();
    cy.get(displayInTotalLossOrdersTab, { timeout: 15000 }).should('be.visible');

    cy.filterNewCarOrder(`E2E${ncoPrefixThisTest}`);
    cy.get('.ag-header').find('[col-id="pk_new_car_order_id"]').click();
    cy.wait('@fetchOrders');

    cy.get('[row-index="0"] [col-id="pk_new_car_order_id"]').contains(inDistributionOrders[0].pk_new_car_order_id);
    cy.get('[row-index="0"] [col-id="order_status_onevms_code"]').contains(CORA_NEW_CAR_ORDER_STATUS_REPORT_TOTAL_LOSS);

    cy.get('[row-index="1"] [col-id="pk_new_car_order_id"]').contains(inDistributionOrders[1].pk_new_car_order_id);
    cy.get('[row-index="1"] [col-id="order_status_onevms_code"]').contains(CORA_NEW_CAR_ORDER_STATUS_REPORT_TOTAL_LOSS);

    //Check that persist schedule was created
    cy.task<boolean>('checkNcoPersistTotalLossSchedule', inDistributionOrders[0].pk_new_car_order_id);
    cy.task('cleanupNcoPersistTotalLossSchedules', [inDistributionOrders[0].pk_new_car_order_id]);
    cy.task<boolean>('checkNcoPersistTotalLossSchedule', inDistributionOrders[1].pk_new_car_order_id);
    cy.task('cleanupNcoPersistTotalLossSchedules', [inDistributionOrders[1].pk_new_car_order_id]);
  });
});
