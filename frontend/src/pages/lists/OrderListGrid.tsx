import {
  ColDef,
  GetLocaleTextParams,
  GridApi,
  GridReadyEvent,
  IServerSideDatasource,
  ITooltipParams,
  ValueGetterParams,
} from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';
import React, { JSX, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useAuthContext } from '../../app/AuthContext';
import { IconName, PButtonPure, PHeading, PLinkPure, PSpinner, PText } from '@porsche-design-system/components-react';
import './etc/ag-grid-compact-mode.css';
import './etc/ag-grid-action-menu.css';
import '@porsche-design-system/components-react/ag-grid/theme.css';
import {
  CoraMdOneVmsStatus,
  CoraMdOneVmsStatusModel,
  CoraMdOneVmsStatusType,
} from '../../../../infrastructure/lib/types/masterdata-types';
import { i18n as i18n_type, TFunction } from 'i18next';
import {
  CoraNCOQueryApiRequestBody,
  FilterModelRequest,
  NCOEventFilters,
} from '../../../../infrastructure/lib/types/new-car-order-types';
import { fetchAgGridSsrmNewCarOrder } from '../../store/api/ServerSideDataSource';
import { displayNotification } from '../../store/slices/NotificationSlice';
import { useAppDispatch } from '../../app/hooks';
import MultiActionContainer from './MultiActionContainer';
import CancelOrdersModal from '../../components/cancel-orders-modal/CancelOrdersModal';
import CopyOrdersModal from '../../components/copy-orders-modal/CopyOrdersModal';
import DeallocateQuotaModal from '../../components/deallocate-quota-modal/DeallocateQuotaModal';
import TotalLossModal from '../../components/total-loss-modal/TotalLossModal';
import UpdateCoreDataModal from '../../components/update-core-data-modal/UpdateCoreDataModal';
import HandleDealerInventoryActionModal from '../../components/handle-dealer-inventory-action-modal/HandleDealerInventoryActionModal';
import ImporterTransferModal from '../../components/importer-transfer-modal/ImporterTransferModal';
import { CommonOrderActionProps } from '../../components/shared/order-action-common-modal/OrderActionCommonModal';
import { useLazyGetModelTypeTextsQuery } from '../../store/api/BossApi';
import { useGetInboundStatusMappingQuery, useGetOnevmsStatusQuery } from '../../store/api/MasterdataApi';
import { CarOrderInList, ModalState, OrderActionType } from '../../store/types';
import { useInIframe } from '../../utils/useInIframe';
import { matchMttForOrderOrPurchaseIntention } from '../../utils/utils';
import Tippy from '@tippyjs/react';
import { useGetStageConfigQuery } from '../../store/api/StaticJsonApi';
import copy from 'copy-to-clipboard';
import { routes } from '../../Constants';
import { useTheme } from '../../utils/useTheme';
import { useFeatureFlagContext } from '../../app/FeatureFlagContext';
import { getAllowedEventKeys } from '../../utils/ncoActionHelper';
import { OneVmsEventKey } from '../../../../infrastructure/lib/types/process-steering-types';
import { OneVmsEventHandlerKey } from '../../../../infrastructure/lib/utils/constants';

interface OrderListGridProps {
  gridId: string;
  tableHeightInVh: number;
  pageName?: string;
  nCOEventFilters?: NCOEventFilters;
  filterModel?: FilterModelRequest;
  disableCopyOrder?: boolean;
  enableCancelOrder?: boolean;
  enableChangeOrder?: boolean;
  disableChangeOrderCoreData?: boolean;
  enableTotalLoss?: boolean;
  enableTotalLossRevoke?: boolean;
  enableDeallocateQuota?: boolean;
  disableDlrInventoryAction?: boolean;
  children?: JSX.Element;
}

const OrderListGrid: React.FC<OrderListGridProps> = (props) => {
  const [gridApi, setGridApi] = useState<GridApi<CarOrderInList> | null>(null);
  const [gridKey, setGridKey] = useState<boolean>(false);
  const { t, i18n } = useTranslation();
  const [selectedOrders, setSelectedOrders] = useState<CarOrderInList[]>([]);
  const [ordersForAction, setOrdersForAction] = useState<CarOrderInList[]>([]);
  // ModalActionType
  const [actionType, setActionType] = useState<OrderActionType | OneVmsEventKey | null>(null);
  const [loadModelTypeTexts, modelTypeTextRes] = useLazyGetModelTypeTextsQuery();

  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const onevmsStatusRes = useGetOnevmsStatusQuery(undefined);
  const inboundMappings = useGetInboundStatusMappingQuery(undefined);
  const theme = useTheme();

  useEffect(() => {
    if (gridApi) {
      gridApi.setFilterModel(null);
    }
    loadModelTypeTexts(i18n.language);
    setGridKey(!gridKey);
  }, [i18n.language, loadModelTypeTexts, props.gridId]);

  // Determine User Permissions and features
  const authContext = useAuthContext();
  const featureFlagContext = useFeatureFlagContext();

  const onGridReady = (params: GridReadyEvent) => {
    setGridApi(params.api);
  };

  const refreshGridData = (purge_grid_cache: boolean = false) => {
    if (gridApi) {
      gridApi.refreshServerSide({ route: undefined, purge: purge_grid_cache });
    }
  };

  const editOrder = (params: any) => {
    navigate(`/orders/edit/${params.data.pk_new_car_order_id}`);
  };

  const handleMultiAction = (action: OrderActionType | OneVmsEventKey) => {
    setActionType(action);
    setOrdersForAction(selectedOrders);
  };

  const onSelectionChanged = useCallback(() => {
    const _selected = gridApi?.getServerSideSelectionState() as {
      selectAll: boolean;
      toggledNodes: string[];
    };
    if (_selected.selectAll) {
      console.error("Select all behaves not as you expect, probably don't want to use it!");
      gridApi?.deselectAll();
      return;
    }
    if (_selected.toggledNodes) {
      if (!gridApi) {
        console.error('No GridApi, how that happend, who knows');
        return;
      }
      const _select_orders = _selected.toggledNodes.map((s) => gridApi.getRowNode(s)!.data!);

      setSelectedOrders(_select_orders);
    } else {
      setSelectedOrders([]);
    }
  }, [gridApi]);

  const closeOrderModal = (state: ModalState): void => {
    setActionType(null);
    // SSRM keeps selection, so clear here
    gridApi?.deselectAll();
    if (state === 'result') {
      refreshGridData();
    }
  };

  const commonModalProps: CommonOrderActionProps = {
    orders: ordersForAction,
    modelTypeTexts: modelTypeTextRes.data,
    closeModal: closeOrderModal,
    actionType: actionType!,
  };

  const ActionMenuCellRenderer = (params: any) => {
    const [optionMenuOpen, setOptionMenuOpen] = useState<boolean>(false);
    const stageConfigRes = useGetStageConfigQuery(undefined);
    const isInIframe = useInIframe();
    const nco = params.data as CarOrderInList;
    const allowedEventKeys = getAllowedEventKeys(inboundMappings.data ?? [], nco);

    const handleMenuClick = (action: OrderActionType | OneVmsEventKey) => {
      if (action === OneVmsEventKey.UPDATE_NCO) {
        editOrder(params);
      } else {
        setOptionMenuOpen(false);
        setActionType(action);
        setOrdersForAction([nco]);
      }
    };
    const handleCopyClick = () => {
      const rowData = columns
        .filter((col) => {
          if (typeof col.field === 'string') {
            return col.field.toLowerCase() !== 'row_actions';
          }
          return true;
        })
        .map((col) => col.field ?? '')
        .map((field) => params.data[field])
        .join(',');
      copy(rowData);
    };

    const getDoriUrl = (ncoId: string): string => {
      const doriOrderDetailsPath = routes.external_urls.doriOrderDetailsRelativePath(ncoId);
      const doriAppPath = isInIframe
        ? `${document.referrer}${routes.external_urls.doriRelativePaddockPath}`
        : `${routes.external_urls.doriFullPath(stageConfigRes.data?.stage)}`;
      return doriAppPath + doriOrderDetailsPath;
    };

    let dropDownActions: {
      name: string;
      translation_label: string;
      action: OrderActionType | OneVmsEventKey;
      icon_name?: IconName;
    }[] = [];
    if (authContext.permissions.NCO_EDIT && props.enableChangeOrder) {
      dropDownActions.push({
        name: 'edit_order',
        translation_label: 'edit_order',
        action: OneVmsEventKey.UPDATE_NCO,
        icon_name: 'edit',
      });
    }

    if (
      featureFlagContext.featureFlags.updateNcoCoreData &&
      authContext.permissions.NCO_EDIT &&
      !props.disableChangeOrderCoreData &&
      //check that nco is allowed for event UPDATE_CORE_DATA
      allowedEventKeys.includes(OneVmsEventKey.UPDATE_CORE_DATA)
    ) {
      dropDownActions.push({
        name: 'update_order_core_data',
        translation_label: 'update_core_data',
        action: OneVmsEventKey.UPDATE_CORE_DATA,
        icon_name: 'edit',
      });
    }
    if (
      authContext.permissions.NCO_CANCEL &&
      props.enableCancelOrder &&
      allowedEventKeys.includes(OneVmsEventKey.CANCEL)
    ) {
      dropDownActions.push({
        name: 'cancel_order',
        translation_label: 'cancel_order',
        action: OneVmsEventKey.CANCEL,
        icon_name: 'delete',
      });
    }
    if (featureFlagContext.featureFlags.copyOrder && authContext.permissions.NCO_COPY && !props.disableCopyOrder) {
      dropDownActions.push({
        name: 'copy_order',
        translation_label: 'copy_order',
        action: OrderActionType.COPY,
        icon_name: 'copy',
      });
    }
    if (
      featureFlagContext.featureFlags.deallocateQuota &&
      authContext.permissions.NCO_DEALLOCATE_QUOTA &&
      props.enableDeallocateQuota &&
      allowedEventKeys.includes(OneVmsEventKey.DEALLOCATE_QUOTA) &&
      params.data.quota_month
    ) {
      dropDownActions.push({
        name: 'deallocate_quota',
        translation_label: 'deallocate_quota',
        action: OneVmsEventKey.DEALLOCATE_QUOTA,
        icon_name: 'delete',
      });
    }
    if (
      featureFlagContext.featureFlags.reportRevokeTotalLoss &&
      authContext.permissions.NCO_REPORT_TOTAL_LOSS &&
      props.enableTotalLoss &&
      allowedEventKeys.includes(OneVmsEventKey.TOTAL_LOSS_REPORT)
    ) {
      dropDownActions.push({
        name: 'report_total_loss',
        translation_label: 'report_total_loss',
        action: OneVmsEventKey.TOTAL_LOSS_REPORT,
        // icon_name: 'report'
      });
    }
    if (
      featureFlagContext.featureFlags.reportRevokeTotalLoss &&
      authContext.permissions.NCO_REVOKE_TOTAL_LOSS &&
      props.enableTotalLossRevoke &&
      allowedEventKeys.includes(OneVmsEventKey.TOTAL_LOSS_REVOKE)
    ) {
      dropDownActions.push({
        name: 'revoke_total_loss',
        translation_label: 'revoke_total_loss',
        action: OneVmsEventKey.TOTAL_LOSS_REVOKE,
      });
    }
    if (
      featureFlagContext.featureFlags.handleDealerInventory &&
      authContext.permissions.NCO_MOVE_TO_DEALER_INVENTORY &&
      !props.disableDlrInventoryAction &&
      allowedEventKeys.includes(OneVmsEventKey.MOVE_TO_INVENTORY)
    ) {
      dropDownActions.push({
        name: 'move_to_dealer_inventory',
        translation_label: 'move_to_dealer_inventory',
        action: OneVmsEventKey.MOVE_TO_INVENTORY,
      });
    }
    if (
      featureFlagContext.featureFlags.handleDealerInventory &&
      authContext.permissions.NCO_REMOVE_FROM_DEALER_INVENTORY &&
      !props.disableDlrInventoryAction &&
      allowedEventKeys.includes(OneVmsEventKey.REMOVE_FROM_INVENTORY)
    ) {
      dropDownActions.push({
        name: 'remove_from_dealer_inventory',
        translation_label: 'remove_from_dealer_inventory',
        action: OneVmsEventKey.REMOVE_FROM_INVENTORY,
      });
    }
    if (featureFlagContext.featureFlags.importerTransfer && authContext.permissions.NCO_TRANSFER_IMPORTER) {
      dropDownActions.push({
        name: 'importer_transfer',
        translation_label: 'importer_transfer',
        action: OrderActionType.IMPORTER_TRANSFER,
      });
    }

    const dropDownContent = (
      <div className="menu-container">
        {dropDownActions.map((a, i) => (
          <div key={i}>
            <div data-e2e={a.name} onClick={() => handleMenuClick(a.action)} className="menu-item">
              {/* {a.icon_name && <PIcon name={a.icon_name} />} */}
              <PText size={'x-small'}>{t(a.translation_label)}</PText>
            </div>
            <div className="menu-divider" />
          </div>
        ))}
      </div>
    );

    if (authContext.isLoading || stageConfigRes.isLoading) {
      return <PSpinner size={'small'} />;
    }
    return (
      <div className={'ag-cell-renderer'}>
        <PLinkPure theme={theme} size={'small'} icon="none" underline={true}>
          <a href={getDoriUrl(nco.pk_new_car_order_id)} target={'_top'}>
            {t('order_details')}
          </a>
        </PLinkPure>
        {dropDownActions.length > 0 && (
          <Tippy
            content={dropDownContent}
            visible={optionMenuOpen}
            onClickOutside={() => setOptionMenuOpen(false)}
            allowHTML={true}
            arrow={false}
            appendTo={document.body}
            interactive={true}
            placement={'bottom-start'}
          >
            <PButtonPure
              data-e2e="open_actions"
              theme={theme}
              icon={'menu-dots-vertical'}
              hideLabel={true}
              onClick={() => setOptionMenuOpen(!optionMenuOpen)}
            />
          </Tippy>
        )}
        <PButtonPure theme={theme} size={'x-small'} icon="copy" hideLabel={true} onClick={handleCopyClick} />
      </div>
    );
  };

  const mttValueGetter = (params: ValueGetterParams<CarOrderInList> | ITooltipParams<CarOrderInList>) => {
    return modelTypeTextRes.isLoading || modelTypeTextRes.isFetching
      ? t('loading')
      : matchMttForOrderOrPurchaseIntention(modelTypeTextRes.data, params.data!, t);
  };

  const columns = useMemo<ColDef<CarOrderInList>[]>(
    () => [
      {
        headerName: t('pk_new_car_order_id'),
        headerTooltip: t('pk_new_car_order_id'),
        field: 'pk_new_car_order_id',
        tooltipField: 'pk_new_car_order_id',
        filterParams: {
          readOnly: false,
        },
        pinned: 'left',
        lockPosition: true,
      },
      {
        headerName: t('business_partner_id'),
        headerTooltip: t('business_partner_id'),
        field: 'business_partner_id',
        tooltipField: 'business_partner_id',
        suppressStickyLabel: true,
      },
      {
        headerName: t('order_type'),
        headerTooltip: t('order_type'),
        field: 'order_type',
        tooltipField: 'order_type',
        filter: 'agTextColumnFilter',
        suppressStickyLabel: true,
      },
      {
        headerName: t('quota_month'),
        headerTooltip: t('quota_month'),
        field: 'quota_month',
        tooltipField: 'quota_month',
      },
      {
        headerName: t('model_year'),
        headerTooltip: t('model_year'),
        field: 'model_year',
        tooltipField: 'model_year',
      },
      {
        headerName: t('model_text'),
        headerTooltip: t('model_text'),
        colId: 'model_text',
        filter: false,
        sortable: false,
        valueGetter: mttValueGetter,
        tooltipValueGetter: mttValueGetter,
      },
      {
        headerName: t('model_type'),
        headerTooltip: t('model_type'),
        field: 'model_type',
        tooltipField: 'model_type',
      },
      {
        headerName: t('dealer_number'),
        headerTooltip: t('dealer_number'),
        field: 'dealer_number',
        tooltipField: 'dealer_number',
      },
      {
        headerName: t('dealer_name'),
        headerTooltip: t('dealer_name'),
        field: 'dealer_name',
        filter: false,
        sortable: false,
        tooltipValueGetter: (params: ITooltipParams<CarOrderInList, string>) =>
          `${params.data?.dealer_name ?? 'MissingName'}`,
      },
      {
        headerName: t('order_status_onevms_code'),
        headerTooltip: t('order_status_onevms_code'),
        field: 'order_status_onevms_code',
        filter: 'agSetColumnFilter',
        filterParams: {
          values:
            onevmsStatusRes?.data
              ?.filter((os) => os.status_type === CoraMdOneVmsStatusType.Order)
              .map((os) => os.status_code) ?? [],
          buttons: ['apply', 'reset'],
          closeOnApply: true,
        },
        tooltipValueGetter: (params: ITooltipParams<CarOrderInList, string>) =>
          params.data ? getOnevmsStatusDescription(params.data, onevmsStatusRes.data ?? [], i18n, t) : '',
      },
      {
        headerName: t('order_status_onevms_error_code'),
        headerTooltip: t('order_status_onevms_error_code'),
        colId: 'order_status_onevms_error_code',
        valueGetter: (params) =>
          params.data?.order_status_onevms_error_code !== 'null' ? params.data?.order_status_onevms_error_code : '',
        filter: 'agSetColumnFilter',
        filterParams: {
          values:
            onevmsStatusRes?.data
              ?.filter((os) => os.status_type === CoraMdOneVmsStatusType.Error)
              .map((os) => os.status_code) ?? [],
          buttons: ['apply', 'reset'],
          closeOnApply: true,
        },
        tooltipValueGetter: (params: ITooltipParams<CarOrderInList, string>) =>
          params.data ? getOnevmsStatusDescription(params.data, onevmsStatusRes.data ?? [], i18n, t) : '',
      },
      {
        headerName: t('created_at'),
        headerTooltip: t('created_at'),
        field: 'created_at',
        tooltipField: 'created_at',
        filter: false,
        hide: true,
        suppressStickyLabel: true,
      },
      {
        headerName: t('created_by'),
        headerTooltip: t('created_by'),
        field: 'created_by',
        tooltipField: 'created_by',
        filter: 'agTextColumnFilter',
        hide: true,
        suppressStickyLabel: true,
      },
      {
        headerName: t('modified_by'),
        headerTooltip: t('modified_by'),
        field: 'modified_by',
        tooltipField: 'modified_by',
        filter: 'agTextColumnFilter',
        hide: true,
        suppressStickyLabel: true,
      },
      {
        headerName: t('modified_at'),
        headerTooltip: t('modified_at'),
        field: 'modified_at',
        tooltipField: 'modified_at',
        filter: false,
        hide: true,
        suppressStickyLabel: true,
      },
      {
        headerName: t('importer_number'),
        headerTooltip: t('importer_number'),
        field: 'importer_number',
        tooltipField: 'importer_number',
        filter: 'agTextColumnFilter',
        hide: true,
        suppressStickyLabel: true,
      },
      {
        headerName: t('shipping_code'),
        headerTooltip: t('shipping_code'),
        field: 'shipping_code',
        tooltipField: 'shipping_code',
        filter: 'agTextColumnFilter',
        hide: true,
        suppressStickyLabel: true,
      },
      {
        headerName: t('receiving_port_code'),
        headerTooltip: t('receiving_port_code'),
        field: 'receiving_port_code',
        tooltipField: 'receiving_port_code',
        filter: 'agTextColumnFilter',
        hide: true,
        suppressStickyLabel: true,
      },
      {
        headerName: t('requested_dealer_delivery_date'),
        headerTooltip: t('requested_dealer_delivery_date'),
        field: 'requested_dealer_delivery_date',
        tooltipField: 'requested_dealer_delivery_date',
        filter: 'agTextColumnFilter',
        hide: true,
        suppressStickyLabel: true,
      },
      {
        headerName: t('row_actions'),
        colId: 'actions',
        filter: false,
        sortable: false,
        minWidth: 180,
        pinned: 'right',
        cellRenderer: ActionMenuCellRenderer,
      },
    ],
    [onevmsStatusRes.data, t, i18n, ActionMenuCellRenderer, mttValueGetter],
  );

  const serverDataSource: IServerSideDatasource = useMemo(() => {
    return {
      async getRows(params) {
        try {
          const res = await fetchAgGridSsrmNewCarOrder({
            ...params.request,
            eventFilters: props.nCOEventFilters && props.nCOEventFilters.length > 0 ? props.nCOEventFilters : undefined,
          } as CoraNCOQueryApiRequestBody);
          if (!res) {
            params.fail();
            return;
          }
          params.success({ rowData: res.data, rowCount: res.rowCount });
          if (!res.data || res.data.length === 0 || res.rowCount === 0) {
            params.api.showNoRowsOverlay();
          } else {
            params.api.hideOverlay();
          }
        } catch (error) {
          if (error instanceof Error) {
            dispatch(
              displayNotification({
                title: `${t('api_error')}: ${t('nco_list')}`,
                msg: error.message,
                state: 'error',
              }),
            );
          } else {
            console.error(error);
          }
          params.fail();
        }
      },
    };
  }, [props.nCOEventFilters]);

  useEffect(() => {
    if (gridApi && props.filterModel) {
      for (const [k, v] of Object.entries(props.filterModel)) {
        gridApi.setColumnFilterModel(k, v).then(() => gridApi.onFilterChanged());
      }
    }
  }, [gridApi, props.filterModel]);

  let actionModal = <></>;

  switch (actionType) {
    case OneVmsEventKey.CANCEL:
      actionModal = <CancelOrdersModal {...commonModalProps}></CancelOrdersModal>;
      break;
    case OrderActionType.COPY:
      actionModal = <CopyOrdersModal {...commonModalProps}></CopyOrdersModal>;
      break;
    case OneVmsEventKey.DEALLOCATE_QUOTA:
      actionModal = <DeallocateQuotaModal {...commonModalProps}></DeallocateQuotaModal>;
      break;
    case OneVmsEventKey.TOTAL_LOSS_REPORT:
    case OneVmsEventKey.TOTAL_LOSS_REVOKE:
      actionModal = <TotalLossModal {...commonModalProps}></TotalLossModal>;
      break;
    case OneVmsEventKey.UPDATE_CORE_DATA:
      actionModal = <UpdateCoreDataModal {...commonModalProps}></UpdateCoreDataModal>;
      break;
    case OneVmsEventKey.MOVE_TO_INVENTORY:
    case OneVmsEventKey.REMOVE_FROM_INVENTORY:
      actionModal = <HandleDealerInventoryActionModal {...commonModalProps}></HandleDealerInventoryActionModal>;
      break;
    case OrderActionType.IMPORTER_TRANSFER:
      actionModal = <ImporterTransferModal {...commonModalProps}></ImporterTransferModal>;
      break;
    default:
      break;
  }

  // Removed flickering, react context rerenders all components using it on change
  if (authContext.isLoading) {
    return <PSpinner />;
  }
  return (
    <>
      <div>
        <div
          style={{
            width: '100%',
            overflow: 'hidden',
            position: 'relative',
          }}
        >
          <PHeading size="medium" style={{ paddingBottom: '1vh', height: '3vh' }}>
            {props.pageName ?? t('orders')}
            <PButtonPure
              data-e2e="refresh_list"
              theme={theme}
              size={'small'}
              icon="refresh"
              underline={false}
              style={{ marginLeft: '20px', verticalAlign: 'middle' }}
              onClick={() => refreshGridData()}
            ></PButtonPure>
          </PHeading>
          {props.children}
          <MultiActionContainer
            handleMultiAction={handleMultiAction}
            selectedOrders={selectedOrders}
            actionProps={{ ...props }}
          />
          <div
            className={`${theme === 'light' ? 'ag-theme-pds' : 'ag-theme-pds-dark'} compact`}
            style={{
              height: props.tableHeightInVh - (selectedOrders.length > 1 ? 4 : 0) + 'vh',
              overflow: 'hidden',
              flex: '1 1 0px',
              width: '100%',
            }}
          >
            <AgGridReact
              // Used to force a rerender so the navigation menu changes language
              key={gridKey ? 'somekey' : 'someotherkey'}
              rowModelType="serverSide"
              onGridReady={onGridReady}
              getRowId={(params) => params.data.pk_new_car_order_id}
              serverSideDatasource={serverDataSource}
              columnDefs={columns}
              animateRows
              defaultColDef={{
                filter: 'agTextColumnFilter',
                enableCellChangeFlash: true,
                filterParams: {
                  defaultOption: 'contains',
                  filterOptions: ['equals', 'notEqual', 'contains', 'notContains', 'startsWith', 'endsWith'],
                  trimInput: true,
                },
                sortable: true,
                resizable: true,
                editable: false,
                floatingFilter: true,
                minWidth: 80,
                flex: 1,
                cellStyle: () => ({
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                }),
                autoHeight: true,
                // Disable Export (Excel/CSV) full list of options: https://www.ag-grid.com/javascript-data-grid/context-menu/
                contextMenuItems: ['copy', 'copyWithHeaders'],
              }}
              onSelectionChanged={onSelectionChanged}
              floatingFiltersHeight={70}
              enableCellTextSelection={true}
              tooltipShowDelay={1000}
              tooltipHideDelay={4000}
              // SelectAll Checkbox removed with CSS, SSRM makes it effectivly unusable
              rowSelection={{ mode: 'multiRow', selectAll: undefined }}
              selectionColumnDef={{
                pinned: 'left',
              }}
              domLayout="normal"
              alwaysShowHorizontalScroll={false}
              suppressHorizontalScroll={false}
              autoSizeStrategy={{
                type: 'fitCellContents',
              }}
              // Pagination props
              pagination={true}
              paginationPageSize={100}
              paginationPageSizeSelector={[20, 100, 500]}
              localeText={{
                page: t('navigate_page'),
                to: t('aggrid.pagination.to'),
                of: t('aggrid.pagination.of'),
                more: t('aggrid.pagination.more'),
                noRowsToShow: t('no_rows_to_show'),
              }}
              suppressMultiSort={true}
            />
          </div>
          {actionModal}
        </div>
      </div>
    </>
  );
};
export default OrderListGrid;

const getOnevmsStatusDescription = (
  nco: CarOrderInList,
  onevmsStatus: CoraMdOneVmsStatusModel[],
  i18n: i18n_type,
  t: TFunction,
): string => {
  const status = onevmsStatus?.find((os) => os.status_code === nco.order_status_onevms_code);
  if (status) {
    return i18n.language === 'de-DE' ? status.status_description_DE : status.status_description_EN;
  } else {
    return t('missing_status');
  }
};
