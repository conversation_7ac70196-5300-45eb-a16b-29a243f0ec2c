import { <PERSON><PERSON><PERSON>, <PERSON>pinner, PWordmark } from '@porsche-design-system/components-react';
import { JSX, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Navigate, Route, Routes, useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import { v4 as uuidv4 } from 'uuid';
import './App.css';
import { routes } from './Constants';
import { useAuthContext } from './app/AuthContext';
import ConfirmationModalContextProvider from './app/ModalConfirmationContext';
import { useAppDispatch, useAppSelector } from './app/hooks';
import { Notification } from './components/notification/Notification';
import { UserMenu } from './components/user/UserMenu';
import AuditMainPage from './pages/audit/AuditMainPage';
import OrdersMainPage from './pages/car-order-flows/OrdersMainPage';
import OrderConvertMainPage from './pages/car-order-flows/order-convert/OrderConvertMainPage';
import OrderCreateMainPage from './pages/car-order-flows/order-create/OrderCreateMainPage';
import OrderEditMainPage from './pages/car-order-flows/order-edit/OrderEditMainPage';
import KccTestComponent from './pages/kcc-component/KccTestComponent';
import FailedOrderListGrid from './pages/lists/FailedOrderPage';
import ListsMainPage from './pages/lists/ListsMainPage';
import OrderListGrid from './pages/lists/OrderListGrid';
import PreProductionListsPage from './pages/lists/PreProductionListsPage';
import PurchaseIntentionListGrid from './pages/lists/PurchaseIntentionListGrid';
import SystemNavigationPage from './pages/lists/SystemNavigationPage';
import { ParameterSelection } from './pages/testing/ParameterSelection';
import './services/i18n';
import { useGetStageConfigQuery } from './store/api/StaticJsonApi';
import { setCorrelationId } from './store/slices/CorrelationSlice';
import { useInIframe } from './utils/useInIframe';
import { useGetOnevmsStatusQuery } from './store/api/MasterdataApi';
import { useFeatureFlagContext } from './app/FeatureFlagContext';
import { OneVmsEventKey } from '../../infrastructure/lib/types/process-steering-types';

type IframePathChangeMessageData = {
  pathname: string;
  search?: string;
};

function App(): JSX.Element {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const isInIframe = useInIframe();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const correlationId = useAppSelector((state) => state.correlation.correlationId);
  const { t } = useTranslation();
  const authContext = useAuthContext();
  const featureFlagContext = useFeatureFlagContext();
  const { data: stageConfig } = useGetStageConfigQuery(undefined);
  const onevmsStatusRes = useGetOnevmsStatusQuery(undefined);

  //send message to iframe parent about path change
  useEffect(() => {
    if (isInIframe && window.parent && stageConfig && location.pathname !== '/') {
      window.parent.postMessage(
        { pathname: location.pathname, search: location.search } satisfies IframePathChangeMessageData,
        routes.external_urls.paddockFullPath(stageConfig.stage),
      );
    }
  }, [location.pathname, stageConfig, isInIframe]);

  useEffect(() => {
    const not_found = searchParams.get('not_found');
    if (not_found) {
      navigate(not_found);
    }
  }, [searchParams]);

  useEffect(() => {
    const correlationIdFromUrl = searchParams.get('correlationId');
    if (correlationIdFromUrl) {
      dispatch(setCorrelationId(correlationIdFromUrl));
    } else {
      if (!correlationId) {
        const newCorrelationId = uuidv4();
        dispatch(setCorrelationId(newCorrelationId));
      }
    }
  }, [searchParams, location, dispatch, correlationId]);

  return (
    <ConfirmationModalContextProvider>
      {!isInIframe && (
        <div className={'nav-user'}>
          <div>
            <UserMenu />
          </div>
        </div>
      )}
      <div>
        <>
          <Notification />
          <div className="app">
            {!isInIframe && (
              <header>
                <PWordmark className="app-logo" />
              </header>
            )}
            <Routes>
              <Route path={routes.testing} element={<ParameterSelection />} />
              <Route path={routes.test} element={<KccTestComponent />} />
              <Route path={`${routes.audit}/:nco_id`} element={<AuditMainPage />} />
              <Route path="/orders" element={<OrdersMainPage />}>
                <Route path={routes.orders.create} element={<OrderCreateMainPage />} />
                <Route path={`${routes.orders.edit}/:nco_id?`} element={<OrderEditMainPage />} />
                <Route path={`${routes.orders.convert}/:purchase_intention_id?`} element={<OrderConvertMainPage />} />
              </Route>
              <Route path="/lists" element={<ListsMainPage />}>
                <Route path={routes.lists.purchaseIntentions} element={<PurchaseIntentionListGrid />} />
                <Route path={routes.lists.preProductionOrders} element={<PreProductionListsPage />} />
                <Route
                  path={routes.lists.inProductionOrders}
                  element={
                    <OrderListGrid
                      gridId="InProductionListGrid"
                      tableHeightInVh={isInIframe ? 88 : 78}
                      filterModel={{
                        order_status_onevms_code: {
                          filterType: 'set',
                          values: (onevmsStatusRes.data?.map((s) => s.status_code) ?? []).filter((s) => s === 'IP0000'),
                        },
                      }}
                      pageName={t('in_production_orders')}
                      nCOEventFilters={[]}
                    />
                  }
                />
                <Route
                  path={routes.lists.inDistributionOrders}
                  element={
                    <OrderListGrid
                      gridId="InDistributionListGrid"
                      tableHeightInVh={isInIframe ? 88 : 78}
                      filterModel={{
                        order_status_onevms_code: {
                          filterType: 'set',
                          values: (onevmsStatusRes.data?.map((s) => s.status_code) ?? []).filter((s) => s === 'ID0000'),
                        },
                      }}
                      pageName={t('in_distribution_orders')}
                      enableTotalLoss={true}
                      nCOEventFilters={[]}
                    />
                  }
                />
                <Route
                  path={routes.lists.atDealershipsOrders}
                  element={
                    <OrderListGrid
                      gridId="AtDealershipsListGrid"
                      tableHeightInVh={isInIframe ? 88 : 78}
                      filterModel={{
                        order_status_onevms_code: {
                          filterType: 'set',
                          values: (onevmsStatusRes.data?.map((s) => s.status_code) ?? []).filter((s) => s === 'ID5000'),
                        },
                      }}
                      nCOEventFilters={[]}
                      pageName={t('at_dealership_orders')}
                      enableTotalLoss={true}
                    />
                  }
                />
                {featureFlagContext.featureFlags.reportRevokeTotalLoss &&
                  authContext.permissions.NCO_REVOKE_TOTAL_LOSS && (
                    <Route
                      path={routes.lists.totalLossOrders}
                      element={
                        <OrderListGrid
                          gridId="TotalLossListGrid"
                          tableHeightInVh={isInIframe ? 88 : 78}
                          nCOEventFilters={[{ event: OneVmsEventKey.TOTAL_LOSS_REVOKE }]}
                          pageName={t('total_loss_orders')}
                          enableTotalLossRevoke={true}
                          disableCopyOrder={true}
                          disableChangeOrderCoreData={true}
                        />
                      }
                    />
                  )}
              </Route>
              <Route path={routes.monitoring} element={<FailedOrderListGrid />} />
              <Route path={routes.navigationSystem} element={<SystemNavigationPage />} />
              <Route path="*" element={<Navigate to={routes.lists.preProductionOrders} />} />
            </Routes>
          </div>
        </>
      </div>
    </ConfirmationModalContextProvider>
  );
}

export default App;
